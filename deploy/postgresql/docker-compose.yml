version: "3.8"
services:
  postgres:
    image: postgres:16-alpine
    environment:
      - POSTGRES_USER=${DATABASE_USERNAME:-curio}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD:-curio123}
      - POSTGRES_DB=${DATABASE_NAME:-curio}
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    volumes:
      - ./init-orleans-sql.sql:/docker-entrypoint-initdb.d/init.sql
      - ./postgres-data:/var/lib/postgresql/data
# volumes:
#   postgres-data:
