# Curio API 生产环境部署指南

## 🚀 生产环境配置最佳实践

### ❌ 不推荐：直接使用 .env 文件
```bash
# 不安全的做法
docker run -d --env-file .env curio-api:latest
```

### ✅ 推荐：多层次配置管理

## 1. 容器化部署

### Docker Compose (生产版本)

创建 `docker-compose.production.yml`：

```yaml
version: "3.8"

services:
  curio-api:
    image: curio-api:${VERSION:-latest}
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      # 从外部环境变量获取敏感信息
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    ports:
      - "80:80"
    depends_on:
      - postgres
      - kafka
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  curio-silo:
    image: curio-silo:${VERSION:-latest}
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - KAFKA_SASL_PASSWORD=${KAFKA_SASL_PASSWORD}
    depends_on:
      - postgres
      - kafka

  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${DATABASE_USERNAME:-curio}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_DB=${DATABASE_NAME:-curio}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deploy/postgresql/init-orleans-sql.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME:-curio}"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
```

### 部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

# 检查必需的环境变量
required_vars=(
    "DATABASE_PASSWORD"
    "JWT_SECRET_KEY"
    "ENCRYPTION_KEY"
    "VERSION"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "Error: Environment variable $var is not set"
        exit 1
    fi
done

# 部署应用
docker-compose -f docker-compose.production.yml up -d

echo "Deployment completed successfully!"
```

## 2. Kubernetes 部署

### Secret 管理

```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: curio-secrets
  namespace: curio
type: Opaque
data:
  database-password: <base64-encoded-password>
  jwt-secret-key: <base64-encoded-key>
  encryption-key: <base64-encoded-key>
  smtp-password: <base64-encoded-password>
```

### ConfigMap

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: curio-config
  namespace: curio
data:
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "curio"
  DATABASE_USERNAME: "curio"
  KAFKA_BROKERS: "kafka-service:9092"
  ORLEANS_CLUSTER_ID: "curio-cluster-prod"
  ORLEANS_SERVICE_ID: "curio-service-prod"
```

### Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curio-api
  namespace: curio
spec:
  replicas: 3
  selector:
    matchLabels:
      app: curio-api
  template:
    metadata:
      labels:
        app: curio-api
    spec:
      containers:
      - name: curio-api
        image: curio-api:latest
        ports:
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: curio-secrets
              key: database-password
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: curio-secrets
              key: jwt-secret-key
        envFrom:
        - configMapRef:
            name: curio-config
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 3. 云服务集成

### Azure Key Vault

```csharp
// Program.cs
var builder = WebApplication.CreateBuilder(args);

if (builder.Environment.IsProduction())
{
    var keyVaultUrl = Environment.GetEnvironmentVariable("AZURE_KEY_VAULT_URL");
    if (!string.IsNullOrEmpty(keyVaultUrl))
    {
        builder.Configuration.AddAzureKeyVault(
            new Uri(keyVaultUrl),
            new DefaultAzureCredential());
    }
}
```

### AWS Systems Manager

```bash
# 设置参数
aws ssm put-parameter \
    --name "/curio/production/database-password" \
    --value "your-secure-password" \
    --type "SecureString"

aws ssm put-parameter \
    --name "/curio/production/jwt-secret-key" \
    --value "your-jwt-secret" \
    --type "SecureString"
```

## 4. 环境变量管理策略

### 服务器级别

```bash
# /etc/environment 或 ~/.bashrc
export DATABASE_PASSWORD="secure-password"
export JWT_SECRET_KEY="secure-jwt-key"
export ENCRYPTION_KEY="secure-encryption-key"
```

### CI/CD 管道

#### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      env:
        DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
        JWT_SECRET_KEY: ${{ secrets.JWT_SECRET_KEY }}
        ENCRYPTION_KEY: ${{ secrets.ENCRYPTION_KEY }}
      run: |
        ./deploy.sh
```

#### Azure DevOps

```yaml
# azure-pipelines.yml
variables:
- group: curio-production-secrets

stages:
- stage: Deploy
  jobs:
  - job: DeployProduction
    steps:
    - script: |
        export DATABASE_PASSWORD=$(DATABASE_PASSWORD)
        export JWT_SECRET_KEY=$(JWT_SECRET_KEY)
        ./deploy.sh
```

## 5. 安全检查清单

### 部署前检查

- [ ] 所有敏感信息都通过环境变量或密钥管理服务配置
- [ ] 配置文件中没有硬编码的密码或密钥
- [ ] 生产环境配置文件已排除版本控制
- [ ] 启用了配置验证和安全检查
- [ ] 数据库连接使用加密传输
- [ ] JWT 密钥长度符合安全要求（至少 256 位）

### 运行时监控

```csharp
// 在 Program.cs 中添加安全验证
var app = builder.Build();

if (app.Environment.IsProduction())
{
    // 验证生产环境安全性
    app.Configuration.ValidateProductionSecurity(app.Environment);
}
```

## 6. 故障排除

### 常见问题

1. **环境变量未设置**
   ```bash
   # 检查环境变量
   printenv | grep -E "(DATABASE|JWT|ENCRYPTION)"
   ```

2. **配置验证失败**
   ```bash
   # 查看应用日志
   docker logs curio-api
   ```

3. **密钥管理服务连接失败**
   ```bash
   # 检查服务账户权限
   # 验证网络连接
   ```

### 调试命令

```bash
# 检查容器环境变量
docker exec curio-api printenv

# 查看配置加载情况
docker logs curio-api | grep -i "configuration"

# 测试健康检查
curl http://localhost/health
```

## 7. 最佳实践总结

1. **永远不要**在生产环境中使用 `.env` 文件
2. **使用**环境变量或云服务密钥管理
3. **启用**配置验证和安全检查
4. **实施**最小权限原则
5. **监控**配置变更和访问日志
6. **定期**轮换密钥和密码
7. **备份**重要配置和密钥

通过遵循这些最佳实践，你的 Curio API 将在生产环境中安全、可靠地运行。
