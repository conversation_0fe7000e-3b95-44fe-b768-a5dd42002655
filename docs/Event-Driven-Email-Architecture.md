# Event-Driven Email Architecture

## Overview

This document describes the event-driven email sending architecture implemented in the Curio API system. The architecture replaces the previous synchronous email sending approach with an asynchronous, event-driven pattern that aligns with the system's Orleans Virtual Actor Model and Event Sourcing principles.

## Architecture Components

### 1. EmailConsumerGrain

**Location**: `src/Curio.Orleans.Grains/Email/EmailConsumerGrain.cs`

The `EmailConsumerGrain` is the core component responsible for:

- Consuming `VerificationCodeGeneratedEvent` events
- Asynchronously sending emails through `IEmailService`
- Tracking email sending status and statistics
- Publishing email result events to Kafka
- Implementing retry logic with exponential backoff

**Key Features**:

- **Idempotent Processing**: Prevents duplicate email sends for the same event
- **Retry Mechanism**: Up to 3 attempts with exponential backoff (1s, 2s, 4s)
- **Status Tracking**: Maintains complete audit trail of email operations
- **Event Publishing**: Publishes success/failure events for external integration

### 2. Email Event Types

**Location**: `src/Curio.Shared/Events/UserEvents.cs`

New domain events for email operations:

```csharp
// Email processing lifecycle events
EmailSendingInitiatedEvent    // Email sending started
EmailSentSuccessfullyEvent    // Email sent successfully
EmailSendingFailedEvent       // Email sending failed (with retry info)
```

### 3. Email Status API

**Location**: `src/Curio.Api/Controllers/EmailController.cs`

New REST endpoints for email status monitoring:

```
GET /api/email/status/{email}/{eventId}  # Get specific email sending status
GET /api/email/stats/{email}             # Get email statistics for user
```

## Data Flow

### New Event-Driven Flow

```mermaid
graph TB
    A[User Requests Verification Code] --> B[VerificationGrain]
    B --> C[Generate Code & Store]
    C --> D[Publish VerificationCodeGeneratedEvent]
    D --> E[Kafka: verification-events]
    D --> F[EmailConsumerGrain.ProcessVerificationCodeEventAsync]
    F --> G[Publish EmailSendingInitiatedEvent]
    G --> H[Kafka: email-events]
    F --> I[Send Email via IEmailService]
    I --> J{Email Success?}
    J -->|Yes| K[Publish EmailSentSuccessfullyEvent]
    J -->|No| L[Retry Logic]
    L --> M{Max Retries?}
    M -->|No| I
    M -->|Yes| N[Publish EmailSendingFailedEvent]
    K --> O[Kafka: email-events]
    N --> O
```

### Key Improvements

1. **🚀 Non-Blocking**: API responses are immediate, not blocked by SMTP operations
2. **🔄 Resilient**: Email failures don't affect main business operations
3. **📊 Observable**: Complete audit trail and monitoring capabilities
4. **⚡ Scalable**: Multiple email consumer instances can process events
5. **🎯 Event-Driven**: Fully aligned with system's event sourcing architecture

## Configuration

### Kafka Topics

The system now uses an additional Kafka topic:

```json
{
  "Kafka": {
    "Topics": [
      "domain-events",
      "verification-events",
      "user-events",
      "email-events" // New: Email processing events
    ]
  }
}
```

### Orleans Grain Registration

EmailConsumerGrain is automatically registered with Orleans and uses:

- **Storage**: Default ADO.NET grain storage
- **Streaming**: Kafka streams for event publishing
- **State**: Tracks processed events and statistics

## Usage Examples

### 1. Send Verification Code (Async)

```csharp
// UserService - Returns immediately
var result = await userService.SendVerificationCodeAsync(new SendVerificationCodeCommand
{
    Email = "<EMAIL>",
    Purpose = "registration"
});
// Returns true immediately, email sent asynchronously
```

### 2. Check Email Sending Status

```bash
# Check specific email sending status
GET /api/email/status/<EMAIL>/event-id-12345

# Response
{
  "eventId": "event-id-12345",
  "email": "<EMAIL>",
  "purpose": "registration",
  "status": "Sent",
  "processedAt": "2023-10-15T10:30:00Z",
  "attemptCount": 1
}
```

### 3. Monitor Email Statistics

```bash
# Get email statistics for user
GET /api/email/stats/<EMAIL>

# Response
{
  "totalProcessed": 5,
  "successfulSent": 4,
  "failed": 1,
  "pending": 0,
  "lastProcessedAt": "2023-10-15T10:30:00Z"
}
```

## Testing

### Integration Tests

**Location**: `tests/Curio.IntegrationTests/EmailEventDrivenTests.cs`

The test suite verifies:

- Email consumer event processing
- Status tracking functionality
- Non-blocking UserService behavior
- Retry mechanism operation

### Running Tests

```bash
# Run email-specific integration tests
dotnet test tests/Curio.IntegrationTests --filter EmailEventDrivenTests

# Run all tests
dotnet test
```

## Migration Notes

### Changes Made

1. **UserService**: Removed direct `IEmailService` dependency and synchronous email calls
2. **VerificationGrain**: Added EmailConsumerGrain triggering after event publishing
3. **New Components**: Added EmailConsumerGrain, EmailController, EmailStatusService
4. **Events**: Added email lifecycle events for complete observability
5. **Configuration**: Added email-events Kafka topic

### Backward Compatibility

- All existing APIs maintain the same interface
- Email sending still works, but now asynchronously
- No breaking changes to client applications

### Performance Impact

- **API Latency**: Significantly improved (no SMTP blocking)
- **System Throughput**: Higher concurrent request handling
- **Resource Usage**: More efficient Orleans grain utilization
- **Reliability**: Better fault isolation and recovery

## Monitoring

### Key Metrics to Monitor

1. **Email Processing Rate**: Events processed per second
2. **Success Rate**: Percentage of successful email deliveries
3. **Retry Frequency**: How often retries are needed
4. **Processing Latency**: Time from event to email delivery
5. **Grain Activation**: EmailConsumerGrain activation patterns

### Kafka Event Monitoring

Monitor the new `email-events` topic for:

- Event volume and throughput
- Processing delays
- Error rates and patterns

## Future Enhancements

1. **Dead Letter Queue**: For permanently failed email events
2. **Priority Queues**: Different priorities for different email types
3. **Batch Processing**: Group multiple emails for efficiency
4. **Template Caching**: Redis-based template caching
5. **External Monitoring**: Integration with monitoring systems

---

This event-driven architecture provides a robust, scalable, and observable email delivery system that aligns perfectly with the Curio API's modern .NET architecture principles.
