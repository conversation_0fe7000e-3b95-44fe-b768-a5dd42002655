# Curio API 快速设置指南

## 🚀 纯 appsettings.json 配置方案

### 📁 配置文件层次结构

```
配置优先级（从低到高）：
1. appsettings.json                 # 基础配置（提交）
2. appsettings.Development.json     # 开发环境配置（提交）
3. appsettings.Production.json      # 生产环境配置（提交，不含敏感信息）
4. appsettings.Local.json          # 个人本地配置（不提交）
5. 用户机密 (User Secrets)          # 开发环境敏感信息
6. 环境变量                         # 系统环境变量（最高优先级）
```

### 🗂️ 配置文件说明

- **appsettings.json** - 基础配置，所有环境共享
- **appsettings.Development.json** - 开发环境特定配置
- **appsettings.Production.json** - 生产环境配置，不包含敏感信息，可以提交
- **appsettings.Local.json** - 个人本地配置，包含敏感信息，不提交到 git
- **appsettings.template.json** - 本地配置模板，用于创建 appsettings.Local.json

## 🔧 快速开始

### 1. 克隆项目后的首次设置

```bash
# 1. 复制个人配置模板
cp appsettings.template.json appsettings.Local.json

# 2. 编辑个人配置
nano appsettings.Local.json  # 或使用你喜欢的编辑器

# 3. 或者使用配置管理脚本
./scripts/config-manager.sh
```

### 2. 个人配置示例 (appsettings.Local.json)

```json
{
  "Application": {
    "Security": {
      "Jwt": {
        "SecretKey": "my-personal-jwt-secret-key-32-characters-minimum"
      },
      "Encryption": {
        "Key": "my-personal-encryption-key-32-chars",
        "Salt": "my-personal-salt-16"
      }
    }
  },

  "Database": {
    "Host": "localhost",
    "Port": 5433,
    "Password": "mypassword"
  },

  "Kafka": {
    "BrokerList": ["localhost:9093", "localhost:9094"]
  },

  "Email": {
    "Smtp": {
      "Host": "smtp.qq.com",
      "Username": "<EMAIL>",
      "Password": "your-app-password"
    },
    "DefaultSender": {
      "FromEmail": "<EMAIL>"
    }
  }
}
```

## 🔄 常见场景

### 场景 1：团队成员使用不同的数据库端口

**张三的 .env.local：**

```bash
DATABASE_PORT=5432
DATABASE_PASSWORD=zhang123
```

**李四的 .env.local：**

```bash
DATABASE_PORT=5433
DATABASE_PASSWORD=li456
```

### 场景 2：某个成员需要连接远程 Kafka

**王五的 .env.local：**

```bash
KAFKA_BROKERS=remote-kafka:9092
KAFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT
KAFKA_SASL_USERNAME=wangwu
KAFKA_SASL_PASSWORD=secret123
```

### 场景 3：测试环境配置变更

**创建 .env.testing：**

```bash
DATABASE_HOST=test-db.company.com
KAFKA_BROKERS=test-kafka.company.com:9092
ORLEANS_CLUSTER_ID=curio-cluster-test
```

**运行测试：**

```bash
ASPNETCORE_ENVIRONMENT=Testing dotnet run
```

## 🛠️ 配置管理工具

### 查看当前配置

启动应用时，开发环境会自动显示配置摘要：

```
=== Configuration Summary ===
Environment: Development

Database:
  Host: localhost
  Port: 5432
  Database: curio
  Username: curio
  Password: ***

Kafka:
  Brokers: localhost:9092
  Consumer Group: orleans-event-streams-dev
  Security Protocol: PLAINTEXT

🔍 Key Configuration Check:
  Database Host: localhost (Source: Environment Variable)
  Database Port: 5432 (Source: Configuration File)
  Kafka Brokers: localhost:9092 (Source: Environment Variable)
  Orleans Cluster: curio-cluster-dev (Source: Configuration File)
```

### 配置调试命令

```bash
# 查看所有环境变量
printenv | grep -E "(DATABASE|KAFKA|ORLEANS|JWT)"

# 检查特定配置文件
cat .env.local

# 验证配置加载
dotnet run --environment Development
```

## 📋 团队协作最佳实践

### ✅ 推荐做法

1. **提交到版本控制的文件：**

   - `.env` - 团队共享的默认配置
   - `.env.development` - 开发环境特定配置
   - `appsettings.json` - 基础应用配置
   - `appsettings.Development.json` - 开发环境应用配置

2. **不提交到版本控制的文件：**

   - `.env.local` - 个人本地配置
   - `.env.*.local` - 个人环境特定配置
   - `appsettings.Production.json` - 生产环境配置

3. **配置变更流程：**

   ```bash
   # 1. 如果是团队共享的配置变更
   git add .env .env.development
   git commit -m "Update team default Kafka brokers"

   # 2. 如果是个人配置
   # 只修改 .env.local，不提交
   ```

### ❌ 避免的做法

1. **不要**在 `.env.local` 中设置团队通用的配置
2. **不要**提交包含个人敏感信息的配置文件
3. **不要**在配置文件中硬编码生产环境的敏感信息

## 🔧 故障排除

### 配置不生效？

1. **检查文件优先级：**

   ```bash
   # 环境变量 > .env.local > .env.development > .env > appsettings.json
   ```

2. **检查文件格式：**

   ```bash
   # 正确格式
   DATABASE_HOST=localhost

   # 错误格式
   DATABASE_HOST = localhost  # 不要有空格
   ```

3. **检查文件编码：**
   ```bash
   # 确保文件是 UTF-8 编码，没有 BOM
   file .env.local
   ```

### 配置冲突？

```bash
# 查看配置来源
dotnet run  # 查看启动时的配置摘要

# 临时禁用某个配置文件
mv .env.local .env.local.bak
dotnet run
```

### 敏感信息泄露？

```bash
# 检查是否意外提交了敏感文件
git status
git log --name-only | grep -E "\.env\.local|secrets"

# 如果意外提交，立即从历史中移除
git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch .env.local' --prune-empty --tag-name-filter cat -- --all
```

## 🚀 生产环境部署

生产环境不使用 `.env` 文件，而是使用：

1. **容器环境变量**
2. **Kubernetes Secrets**
3. **云服务密钥管理**

详见 [Production-Deployment-Guide.md](Production-Deployment-Guide.md)

---

这个配置方案让每个团队成员都能灵活地配置自己的本地环境，同时保持团队配置的一致性。
