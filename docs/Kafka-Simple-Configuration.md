# Kafka 简化配置指南

## 概述

Curio项目统一使用SASL用户名密码认证，所有环境配置一致，简单易用。

## 统一配置策略

- **安全协议**: `SASL_PLAINTEXT` (SASL认证，无SSL加密)
- **认证机制**: `PLAIN` (用户名密码认证)
- **适用环境**: 开发、测试、生产环境统一

## 配置格式

### 统一配置模板

所有环境都使用相同的配置格式，只需要修改具体的值：

```json
{
  "Kafka": {
    "BrokerList": ["your-kafka-server:9092"],
    "ConsumerGroupId": "orleans-event-streams",
    "Topics": ["domain-events", "verification-events", "user-events"],
    "SecurityProtocol": "SASL_PLAINTEXT",
    "SaslMechanism": "PLAIN", 
    "SaslUsername": "your-username",
    "SaslPassword": "your-password"
  }
}
```

### 环境差异

只有以下几个参数在不同环境中有差异：

**开发环境**:
- BrokerList: `["localhost:9092"]`
- ConsumerGroupId: `"orleans-event-streams-dev"`

**生产环境**:
- BrokerList: `["kafka-prod:9092"]` 或多个节点
- ConsumerGroupId: `"orleans-event-streams-prod"`

## 部署配置

### 需要配置的参数

部署时只需要设置以下参数：

1. **Kafka服务器地址**
   ```json
   "BrokerList": ["your-kafka-server:9092"]
   ```

2. **认证信息**
   ```json
   "SaslUsername": "your-kafka-username",
   "SaslPassword": "your-kafka-password"
   ```

### 环境变量方式（推荐）

```bash
export Kafka__SaslUsername="kafka-user"
export Kafka__SaslPassword="kafka-password"
export Kafka__BrokerList__0="kafka-server:9092"
```

## 配置文件位置

需要配置的文件：
- `src/Curio.Api/appsettings.json`
- `src/Curio.Api/appsettings.Development.json`
- `src/Curio.Api/appsettings.Production.json`
- `src/Curio.Orleans.Silo/appsettings.json`
- `src/Curio.Orleans.Silo/appsettings.Development.json`
- `src/Curio.Orleans.Silo/appsettings.Production.json`

## 故障排除

### 常见问题

1. **SASL认证失败**
   - 检查用户名密码是否正确
   - 确认Kafka服务器启用了SASL认证

2. **连接失败**
   - 检查BrokerList地址是否正确
   - 确认网络连通性

3. **配置警告消失**
   - 现在所有环境都明确配置了SecurityProtocol
   - 不会再出现SASL配置警告

## 测试配置

### 验证步骤

1. 启动Orleans Silo
2. 检查日志中Kafka连接成功消息
3. 测试事件发布功能

### 开发环境测试

```bash
# 启动本地Kafka
cd deploy/kafka
docker-compose up -d

# 启动应用
cd src/Curio.Orleans.Silo
dotnet run --environment Development
```

## 安全说明

- 使用SASL_PLAINTEXT协议，数据传输未加密
- 适合内网环境或受信任的网络
- 如需更高安全性，可升级为SASL_SSL
- 建议通过环境变量管理敏感信息
