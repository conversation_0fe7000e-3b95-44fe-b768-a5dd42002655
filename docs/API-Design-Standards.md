# Curio API 设计规范文档

## 📋 文档信息

- **项目名称**: Curio API
- **规范版本**: v1.0
- **创建日期**: 2025-08-24
- **基于标准**: REST API、OpenAPI 3.1、RFC 7807、JSON:API

## 🎯 设计原则

### 核心原则

1. **一致性**: 统一的命名、结构和行为模式，**所有 API 响应使用 `{ success, code, message, data }` 格式**
2. **可预测性**: 开发者能够直观理解 API 行为
3. **向后兼容**: 版本升级不破坏现有客户端
4. **自描述性**: API 响应包含足够的元数据
5. **安全优先**: 默认安全，最小权限原则

### RESTful 设计

- 使用 HTTP 动词表达操作意图
- 资源导向的 URL 设计
- 无状态通信
- 统一接口约束

#### HTTP 方法语义

| 方法   | 用途     | 幂等性 | 推荐响应           | 说明                       |
| ------ | -------- | ------ | ------------------ | -------------------------- |
| GET    | 获取资源 | ✅     | 200 OK             | 查询操作，不修改服务器状态 |
| POST   | 创建资源 | ❌     | 201 Created        | 创建新资源，非幂等         |
| PUT    | 完整更新 | ✅     | 200 OK             | 替换整个资源               |
| PATCH  | 部分更新 | ❌     | 200 OK             | 修改资源的部分字段         |
| DELETE | 删除资源 | ✅     | **204 No Content** | 删除资源，推荐无响应体     |

## 🌐 URL 设计规范

### 基础结构

```
https://api.curio.com/api/{resource}/{id}/{sub-resource}
```

### 命名约定

- 使用复数形式的资源名称
- 使用小写字母和连字符
- 避免在 URL 中使用动词
- 子资源通过路径层级表示

### 查询参数规范

- 分页：`page`, `size`, `sort`
- 过滤：使用资源字段名作为参数
- 搜索：使用 `q` 参数
- 日期范围：使用 `[gte]`, `[lt]` 等操作符

## 📊 HTTP 状态码规范

### 成功响应 (2xx)

- **200 OK**: 成功获取资源
- **201 Created**: 成功创建资源
- **202 Accepted**: 请求已接受，异步处理中
- **204 No Content**: 成功执行，无返回内容（如 DELETE）

### 客户端错误 (4xx)

- **400 Bad Request**: 请求格式错误
- **401 Unauthorized**: 未认证
- **403 Forbidden**: 已认证但无权限
- **404 Not Found**: 资源不存在
- **405 Method Not Allowed**: HTTP 方法不支持
- **409 Conflict**: 资源冲突
- **422 Unprocessable Entity**: 请求格式正确但语义错误
- **429 Too Many Requests**: 请求频率超限

### 服务器错误 (5xx)

- **500 Internal Server Error**: 服务器内部错误
- **502 Bad Gateway**: 网关错误
- **503 Service Unavailable**: 服务不可用
- **504 Gateway Timeout**: 网关超时

## 📝 请求/响应格式

### 统一响应格式

所有 API 响应必须遵循以下格式：

```json
{
  "success": boolean,    // 操作是否成功
  "code": number,        // 业务状态码
  "message": string,     // 响应消息
  "data": object|null    // 响应数据
}
```

### 成功响应示例

```json
{
  "success": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": "789",
    "customerId": "123",
    "status": "pending",
    "totalAmount": 59.98,
    "createdAt": "2025-08-24T10:30:00Z"
  }
}
```

### 删除操作响应

#### 推荐方案：HTTP 204 No Content

- 符合 RESTful 最佳实践
- 语义清晰，无需返回内容
- 更高效，减少网络传输

#### 备选方案：HTTP 200 OK

当需要保持响应格式一致性时使用：

```json
{
  "success": true,
  "code": 20003,
  "message": "Order deleted successfully",
  "data": null
}
```

### 列表响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "Success",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

## ❌ 错误处理规范

### 错误响应格式

```json
{
  "success": false,
  "code": 40001,
  "message": "Error description",
  "data": null | { "errors": [...] }
}
```

### 常见错误类型

- **业务错误**: `{ "success": false, "code": 40001, "message": "Invalid email address", "data": null }`
- **验证错误**: 包含详细的字段错误信息
- **资源不存在**: `{ "success": false, "code": 40401, "message": "Order not found", "data": null }`
- **权限错误**: `{ "success": false, "code": 40301, "message": "Access denied", "data": null }`
- **服务器错误**: `{ "success": false, "code": 50001, "message": "Internal server error", "data": null }`

### 验证错误详细格式

```json
{
  "success": false,
  "code": 40001,
  "message": "Validation failed",
  "data": {
    "errors": [
      {
        "field": "email",
        "message": "Email is required",
        "value": ""
      }
    ]
  }
}
```

### 错误码规范

采用"HTTP 状态码 + 两位业务码"的模式：

#### 成功响应 (2xxxx)

- 20000 - 操作成功
- 20001 - 创建成功
- 20002 - 更新成功
- 20003 - 删除成功

#### 客户端错误 (4xxxx)

**400xx - 请求错误**

- 40001 - 参数验证失败
- 40002 - 参数格式错误
- 40003 - 参数缺失

**401xx - 认证错误**

- 40101 - Token 无效
- 40102 - Token 过期
- 40103 - Token 缺失

**403xx - 权限错误**

- 40301 - 权限不足
- 40302 - 账户被禁用

**404xx - 资源不存在**

- 40401 - 资源不存在
- 40402 - 接口不存在

**409xx - 资源冲突**

- 40901 - 资源已存在
- 40902 - 邮箱已被注册

**422xx - 业务逻辑错误**

- 42201 - 业务规则验证失败
- 42202 - 状态转换无效

**429xx - 限流错误**

- 42901 - 请求频率超限
- 42902 - 并发请求过多

#### 服务器错误 (5xxxx)

**500xx - 内部错误**

- 50001 - 内部服务器错误
- 50002 - 数据库连接失败

**502xx - 网关错误**

- 50201 - 上游服务不可用
- 50202 - 服务响应超时

**503xx - 服务不可用**

- 50301 - 服务维护中
- 50302 - 服务过载

### 错误码使用指南

#### 选择错误码的步骤

1. **确定 HTTP 状态码**：根据错误类型选择合适的 HTTP 状态码
2. **选择业务码**：根据具体错误情况选择对应的两位业务码

#### 错误码映射表

| HTTP 状态码 | 业务错误码范围 | 用途         | 示例                   |
| ----------- | -------------- | ------------ | ---------------------- |
| 400         | 400xx          | 请求参数错误 | 40001-参数验证失败     |
| 401         | 401xx          | 认证错误     | 40101-Token 无效       |
| 403         | 403xx          | 权限错误     | 40301-权限不足         |
| 404         | 404xx          | 资源不存在   | 40401-资源不存在       |
| 409         | 409xx          | 资源冲突     | 40901-资源已存在       |
| 422         | 422xx          | 业务逻辑错误 | 42201-业务规则验证失败 |
| 429         | 429xx          | 限流错误     | 42901-请求频率超限     |
| 500         | 500xx          | 内部错误     | 50001-内部服务器错误   |
| 502         | 502xx          | 网关错误     | 50201-上游服务不可用   |
| 503         | 503xx          | 服务不可用   | 50301-服务维护中       |

## 🔐 认证和授权

### 认证方式

- **JWT Bearer Token**: `Authorization: Bearer <token>`
- **API Key**: `X-API-Key: <key>` (备选方案)

### 权限控制错误

```json
{
  "success": false,
  "code": 40301,
  "message": "Insufficient permissions",
  "data": {
    "requiredPermissions": ["orders:read", "orders:write"]
  }
}
```

## 📅 版本控制

### 版本控制方式

1. **Header 版本控制** (推荐)

   - 请求头：`API-Version: 1.0`
   - 响应头：`API-Version: 1.0`, `API-Supported-Versions: 1.0, 1.1, 2.0`

2. **查询参数版本控制** (备选)

   - `GET /api/orders/123?version=1.0`

3. **URL 版本控制** (兼容性支持)
   - `GET /api/v1/orders/123`

### 版本策略

- **默认版本**: 不指定版本时使用当前稳定版本
- **版本协商**: 支持多种版本控制方式
- **生命周期管理**: 通过响应头指示版本状态

### 语义化版本控制

```
主版本.次版本.修订版本 (例如: 1.2.3)

1.x.x - 破坏性变更
x.1.x - 向后兼容的功能添加
x.x.1 - 向后兼容的问题修复
```

### 版本废弃通知

通过响应头通知版本废弃信息：

- `API-Deprecated-Versions: 0.9`
- `Sunset: Wed, 31 Dec 2025 23:59:59 GMT`
- `Warning: 299 - "API version 1.0 will be deprecated on 2025-12-31"`

## 🔍 过滤、排序和分页

### 查询参数

- **过滤**: `?status=pending`, `?status=pending,processing`
- **范围**: `?totalAmount[gte]=100&totalAmount[lt]=500`
- **排序**: `?sort=createdAt:desc`, `?sort=status:asc,createdAt:desc`
- **分页**: `?page=1&size=20` 或 `?cursor=xxx&limit=20`

### 复杂查询

对于复杂查询条件，使用 POST 方式，支持 JSON 格式的过滤、排序和分页参数。

## 🚀 性能优化

### 字段控制

- **字段选择**: `?fields=id,status,totalAmount`
- **关联加载**: `?include=customer,items`
- **缓存控制**: 支持标准 HTTP 缓存机制

## 📊 批量操作

### 批量端点

- `POST /api/orders/batch` - 批量创建
- `PATCH /api/orders/batch` - 批量更新
- `DELETE /api/orders/batch` - 批量删除

批量操作返回成功和失败的详细统计信息。

## 🔄 异步操作

### 长时间运行的操作

对于耗时操作，返回 202 Accepted 状态码：

```json
{
  "success": true,
  "code": 202,
  "message": "Request accepted for processing",
  "data": {
    "jobId": "abc123",
    "status": "processing",
    "statusUrl": "/api/jobs/abc123"
  }
}
```

### 作业状态查询

通过 `GET /api/jobs/{jobId}` 查询作业状态：

- `processing`: 处理中
- `completed`: 已完成
- `failed`: 失败

## 🔔 Webhook 规范

### Webhook 事件格式

```json
{
  "id": "evt_abc123",
  "type": "order.created",
  "version": "1.0",
  "timestamp": "2025-08-24T10:30:00Z",
  "data": {
    "type": "order",
    "id": "789",
    "attributes": {
      /* order data */
    }
  }
}
```

### Webhook 安全

- 签名验证：`X-Curio-Signature`
- 时间戳：`X-Curio-Timestamp`
- 幂等性：`X-Curio-Delivery-Id`

## 📖 OpenAPI 规范

### 基础信息

使用 OpenAPI 3.1.0 规范，包含以下基础信息：

- 标题、版本、描述
- 联系信息和许可证
- 服务器环境配置

### 核心组件

#### 统一响应格式

```yaml
ApiResponse:
  type: object
  required: [success, code, message]
  properties:
    success:
      type: boolean
      description: 操作是否成功
    code:
      type: integer
      description: 业务状态码
    message:
      type: string
      description: 响应消息
    data:
      description: 响应数据
```

#### 错误响应格式

```yaml
ErrorResponse:
  allOf:
    - $ref: "#/components/schemas/ApiResponse"
    - properties:
        success:
          enum: [false]
        code:
          minimum: 40001
          maximum: 59999
```

### 文档要求

- 使用 OpenAPI 3.1.0 规范
- 包含完整的请求/响应示例
- 提供详细的错误码说明
- 支持多种响应格式的定义

### 响应格式一致性

所有 API 响应都必须遵循统一的 `{ success, code, message, data }` 格式。

#### ✅ 正确的响应格式

```json
{
  "success": true,
  "code": 20001,
  "message": "Operation completed successfully",
  "data": {
    /* 具体数据 */
  }
}
```

#### ❌ 错误的响应格式

避免使用不一致的字段名：

- `isSuccess` → 应该是 `success`
- `statusCode` → 应该是 `code`
- `msg` → 应该是 `message`
- `result` → 应该是 `data`

## 🧪 测试规范

### 测试用例要求

- **正常流程测试**: 验证成功场景的响应格式
- **错误场景测试**: 验证各种错误情况的处理
- **边界条件测试**: 空数据集、最大/最小值、特殊字符
- **并发测试**: 验证并发访问的安全性
- **性能测试**: 响应时间和吞吐量测试

## ✅ 数据验证规范

### 输入验证规则

#### 基础验证要求

- **字符串验证**: 必填、长度限制、格式验证
- **数值验证**: 范围检查、精度控制
- **邮箱验证**: 标准邮箱格式
- **电话验证**: 国际格式，带国家代码
- **自定义验证**: 业务规则验证

### 数据格式标准

#### 日期时间格式

- **ISO 8601 UTC 格式**: `2025-08-24T10:30:00Z`
- **带时区信息**: `2025-08-24T18:30:00+08:00`
- **日期格式**: `1990-05-15` (YYYY-MM-DD)
- **时间格式**: `14:30:00` (HH:mm:ss)

#### 货币和数值格式

- **价格**: 使用数值类型，不使用字符串
- **货币代码**: 使用 ISO 4217 标准
- **百分比**: 使用数值，不带 % 符号
- **整数**: 直接使用数值类型

#### 联系信息格式

- **邮箱**: 标准邮箱格式
- **电话**: 国际格式，带国家代码
- **网址**: 完整 URL 格式
- **地址**: 结构化地址对象，国家代码使用 ISO 3166-1 alpha-2

### 验证错误响应

验证失败时返回详细的错误信息，包括：

- 错误字段名
- 错误代码
- 错误消息
- 错误值
- 约束条件（如适用）

## 📖 API 文档生成规范

### 文档要求

- **OpenAPI/Swagger 配置**: 包含基础信息、认证配置、XML 注释
- **代码注释规范**: 控制器和 DTO 需要详细的 XML 注释
- **示例数据**: 提供统一的示例数据标准
- **项目配置**: 启用文档生成和 XML 注释

### 最佳实践

- 使用 Swagger UI 提供交互式文档
- 包含完整的请求/响应示例
- 提供详细的错误码说明
- 支持多环境配置

## 🔄 幂等性设计

### 幂等性概念

幂等性是指同一个操作执行多次与执行一次的效果相同。在分布式系统中，API 必须支持幂等性以防止重复操作。

### 幂等性分类

#### 天然幂等的操作

- **GET**: 查询操作天然幂等
- **PUT**: 完整资源替换，多次执行结果相同
- **DELETE**: 删除操作，推荐返回 204 No Content

#### 需要特殊处理的操作

- **POST**: 创建操作，需要幂等性设计
- **PATCH**: 部分更新操作，需要幂等性设计

### 幂等性实现方案

#### Idempotency-Key Header (推荐)

使用 `Idempotency-Key` 请求头实现幂等性：

```http
POST /api/orders
Idempotency-Key: 550e8400-e29b-41d4-a716-************
Content-Type: application/json
```

#### 实现要点

1. **幂等性键生成**: 客户端生成 UUID 或基于业务数据生成
2. **缓存策略**: 根据操作类型设置不同的缓存时间
3. **错误处理**: 区分验证错误、业务错误和系统错误
4. **响应头**: 返回 `X-Idempotency-Cached` 标识是否来自缓存

#### 缓存时间建议

- 订单创建: 24 小时
- 支付处理: 7 天
- 用户注册: 1 小时
- 数据更新: 30 分钟

### 关键业务场景

- **订单创建**: 使用唯一的幂等性键防止重复下单
- **支付处理**: 确保支付操作的幂等性，避免重复扣款
- **用户注册**: 防止重复注册相同邮箱的用户

### 响应头规范

- `Idempotency-Key`: 返回原始的幂等性键
- `X-Idempotency-Cached`: 标识响应是否来自缓存

## 📊 监控和可观测性

### 请求追踪

- **请求头**: `X-Trace-Id`, `X-Request-Id`
- **响应头**: 返回相同的追踪 ID 和响应时间

### 健康检查

提供 `/api/health` 端点，返回系统健康状态：

- 系统整体状态
- 各组件状态（数据库、缓存、消息队列等）
- 响应时间统计

## 🔒 安全最佳实践

### 输入验证

- 严格的数据类型验证
- 长度限制和格式验证
- SQL 注入和 XSS 防护

### 输出安全

- 敏感数据脱敏
- 错误信息不泄露内部信息
- 使用适当的 HTTP 安全头

## 📚 文档和 SDK

### API 文档要求

- 完整的 OpenAPI 规范
- 交互式 API 文档（Swagger UI）
- 错误代码说明和变更日志

### SDK 设计原则

- 与 API 保持一致的命名
- 强类型支持和异步操作
- 错误处理封装和重试机制

## 🚀 部署和发布

### 发布流程

1. 功能开发和单元测试
2. API 集成测试
3. 文档更新
4. 测试环境验证
5. 灰度发布和监控
6. 版本标记

### 向后兼容性

- 不删除现有字段
- 不修改现有字段类型
- 新增字段设为可选
- 保持错误代码一致性

---

## 📞 联系信息

**API 团队**
**邮箱**: <EMAIL>
**文档**: https://docs.curio.com/api
**最后更新**: 2025-08-24
