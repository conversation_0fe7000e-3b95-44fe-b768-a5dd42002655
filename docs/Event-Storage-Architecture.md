# 事件存储架构说明

## 🎯 核心设计原则

本项目采用 **Kafka 作为唯一事件日志系统** 的架构设计，这是一个重要的架构决策。

## 📊 架构对比

### 传统双写模式（❌ 不采用）
```
Orleans Grain → PostgreSQL Event Store + Kafka Topics
                     ↓                    ↓
                事件持久化              事件分发
```

**问题**：
- 双写一致性问题
- 数据冗余
- 维护复杂度高
- 可能出现数据不一致

### 本项目架构（✅ 采用）
```
Orleans JournaledGrain → Orleans Streams → Kafka Topics
                                              ↓
                                        唯一事件日志
                                              ↓
                                    ┌─────────┼─────────┐
                                    ↓         ↓         ↓
                            投影重建    外部集成    事件重放
```

**优势**：
- 单一事实来源
- 原子性保证
- 架构简洁
- 高性能和可扩展性

## 🗄️ 数据存储职责分工

### PostgreSQL 职责
- **Orleans 集群管理**: 成员管理、故障检测
- **Orleans Grain 状态**: JournaledGrain 的快照状态
- **读模型存储**: 查询优化的投影数据
- **Orleans Reminders**: 定时任务管理

### Kafka 职责
- **事件日志存储**: 所有领域事件的持久化
- **事件分发**: 实时事件流处理
- **外部集成**: 其他服务直接消费事件
- **事件重放**: 支持从任意时间点重建状态

### RediSearch 职责
- **全文搜索**: 复杂查询和搜索功能
- **索引存储**: 搜索优化的数据结构

## 🔄 事件流转过程

### 1. 事件产生
```csharp
// Orleans JournaledGrain
public class OrderGrain : JournaledGrain<OrderState, OrderEvent>
{
    public async Task CreateOrderAsync(CreateOrderCommand command)
    {
        var orderCreated = new OrderCreatedEvent
        {
            OrderId = command.OrderId,
            CustomerId = command.CustomerId,
            // ... 其他属性
        };
        
        // 直接发布到 Kafka (通过 Orleans Streams)
        RaiseEvent(orderCreated);
    }
}
```

### 2. 事件持久化
- Orleans Streams 自动将事件发布到 Kafka Topics
- Kafka 确保事件的持久化和分区
- 无需手动管理数据库事件表

### 3. 事件消费
```csharp
// 投影更新
public class OrderProjectionGrain : Grain, IStreamSubscriptionObserver<OrderEvent>
{
    public async Task OnNextAsync(OrderEvent orderEvent, StreamSequenceToken token)
    {
        // 更新读模型
        await UpdateReadModel(orderEvent);
    }
}
```

## 📋 数据库表结构

### Orleans 核心表
```sql
-- 集群成员管理
OrleansMembershipTable
OrleansMembershipVersionTable

-- Grain 状态存储
OrleansStorage

-- 定时任务
OrleansRemindersTable
```

### 业务读模型表（示例）
```sql
-- 订单读模型
Orders
OrderItems
OrderProjections

-- 客户读模型
Customers
CustomerProfiles
```

### ❌ 不包含的表
```sql
-- 不维护这些事件存储表
-- OrleansEvents (事件存储在 Kafka 中)
-- DomainEvents (事件存储在 Kafka 中)
-- EventStore (事件存储在 Kafka 中)
```

## 🚀 架构优势

### 1. 简化的数据流
- 事件只写入一个地方（Kafka）
- 消除双写复杂性
- 减少数据不一致的风险

### 2. 高性能
- Kafka 的高吞吐量
- Orleans Streams 的异步处理
- 减少数据库写入压力

### 3. 可扩展性
- Kafka 的水平扩展能力
- Orleans 的分布式特性
- 独立的读写扩展

### 4. 运维简化
- 减少存储系统数量
- 统一的监控和备份策略
- Kafka 的成熟运维工具链

## 🔧 配置要点

### Orleans Silo 配置
```csharp
// 只配置 Grain 状态存储，不配置 EventStore
.AddAdoNetGrainStorage("Default", options =>
{
    options.ConnectionString = connectionString;
})
// 配置 Kafka Streams
.AddKafka("KafkaStreams")
.WithOptions(options =>
{
    options.BrokerList = kafkaBrokers;
})
```

### Kafka Topics 配置
```yaml
domain-events:
  partitions: 12
  replication-factor: 3
  
projection-rebuild:
  partitions: 6
  replication-factor: 3
```

## 📚 最佳实践

1. **使用 JournaledGrain**: 所有需要事件溯源的 Grain 都应继承 `JournaledGrain<TState, TEvent>`
2. **事件设计**: 设计不可变的事件结构，支持版本演进
3. **投影更新**: 通过 Orleans Streams 异步更新读模型
4. **幂等性**: 确保事件处理的幂等性
5. **监控**: 监控 Kafka 的健康状态和事件处理延迟

## 🔍 故障排查

### 常见问题
1. **事件丢失**: 检查 Kafka 配置和 Orleans Streams 连接
2. **投影延迟**: 监控 Stream 消费者的处理速度
3. **状态不一致**: 验证事件处理的幂等性

### 监控指标
- Kafka Topic 的消息积压
- Orleans Streams 的处理延迟
- 投影更新的成功率

---

这种架构设计确保了系统的简洁性、一致性和高性能，是现代事件驱动架构的最佳实践。
