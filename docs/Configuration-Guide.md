# Curio API 配置指南

本文档详细说明了 Curio API 项目的配置管理方案，包括配置结构、环境管理、安全性和最佳实践。

## 配置架构概览

### 配置层次结构

```
配置系统
├── 基础配置 (appsettings.json)
├── 环境特定配置 (appsettings.{Environment}.json)
├── 环境变量 (.env, 系统环境变量)
├── 用户机密 (secrets.json)
└── 配置模板 (appsettings.template.json)
```

### 配置优先级

1. **环境变量** - 最高优先级
2. **用户机密** - 开发环境敏感信息
3. **环境特定配置文件** - appsettings.{Environment}.json
4. **基础配置文件** - appsettings.json
5. **默认值** - 代码中定义的默认值

## 配置节点说明

### 1. Application 配置

```json
{
  "Application": {
    "Name": "Curio API",
    "Version": "1.0.0",
    "Environment": "Development",
    "Api": {
      "BaseUrl": "https://localhost:7274",
      "AllowedHosts": ["*"],
      "RequestTimeoutSeconds": 30,
      "MaxRequestBodySize": 10485760,
      "Cors": {
        "Enabled": true,
        "AllowedOrigins": ["http://localhost:3000"],
        "AllowedMethods": ["GET", "POST", "PUT", "DELETE"],
        "AllowedHeaders": ["*"],
        "AllowCredentials": true
      }
    },
    "Security": {
      "Jwt": {
        "SecretKey": "your-secret-key",
        "Issuer": "Curio",
        "Audience": "Curio.Api",
        "ExpirationMinutes": 60,
        "RefreshTokenExpirationDays": 7
      },
      "Encryption": {
        "Key": "your-encryption-key",
        "Salt": "your-salt"
      }
    }
  }
}
```

### 2. Database 配置

```json
{
  "Database": {
    "ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=curio;Password=password",
    "Host": "localhost",
    "Port": 5432,
    "Database": "curio",
    "Username": "curio",
    "Password": "password",
    "CommandTimeout": 30,
    "MaxRetryCount": 3,
    "EnableSensitiveDataLogging": false
  }
}
```

**说明：**
- `ConnectionString` 优先使用，如果为空则从其他字段构建
- 生产环境中 `EnableSensitiveDataLogging` 应设为 `false`

### 3. Orleans 配置

```json
{
  "Orleans": {
    "ClusterId": "curio-cluster",
    "ServiceId": "curio-service",
    "Clustering": {
      "Provider": "AdoNet",
      "ConnectionString": "",
      "RefreshPeriod": 30,
      "DeathVoteExpirationTimeout": 120
    },
    "Storage": {
      "DefaultProvider": "AdoNet",
      "ConnectionString": "",
      "UseJsonFormat": true
    },
    "Streaming": {
      "Provider": "Kafka",
      "ConnectionString": "localhost:9092"
    },
    "Reminders": {
      "Provider": "AdoNet",
      "ConnectionString": ""
    }
  }
}
```

**说明：**
- 如果 Orleans 各组件的 `ConnectionString` 为空，将自动使用数据库连接字符串
- 支持不同的存储提供程序：AdoNet、Memory、Azure 等

### 4. Kafka 配置

```json
{
  "Kafka": {
    "BrokerList": ["localhost:9092"],
    "ConsumerGroupId": "orleans-event-streams",
    "Topics": ["domain-events", "verification-events", "user-events"],
    "SessionTimeoutMs": 30000,
    "HeartbeatIntervalMs": 3000,
    "AutoOffsetReset": "earliest",
    "EnableAutoCommit": true,
    "AutoCommitIntervalMs": 5000,
    "MaxPollRecords": 500,
    "FetchMinBytes": 1,
    "FetchMaxWaitMs": 500,
    "SecurityProtocol": "PLAINTEXT",
    "SaslMechanism": "PLAIN",
    "SaslUsername": "",
    "SaslPassword": ""
  }
}
```

### 5. Email 配置

```json
{
  "Email": {
    "Smtp": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "Username": "<EMAIL>",
      "Password": "your-app-password",
      "EnableSsl": true,
      "TimeoutSeconds": 30,
      "UseDefaultCredentials": false
    },
    "Templates": {
      "TemplatesDirectory": "EmailTemplates",
      "EnableCaching": true,
      "CacheExpirationMinutes": 60
    },
    "DefaultSender": {
      "FromEmail": "<EMAIL>",
      "FromName": "Curio",
      "ReplyToEmail": "<EMAIL>",
      "ReplyToName": "Curio Support"
    },
    "Retry": {
      "MaxAttempts": 3,
      "DelayMilliseconds": 1000,
      "ExponentialBackoff": true
    }
  }
}
```

## 环境管理

### 开发环境 (Development)

- 使用 `appsettings.Development.json`
- 敏感信息使用用户机密或环境变量
- 启用详细日志记录
- 数据库敏感数据日志记录可启用

### 生产环境 (Production)

- 使用环境变量配置所有敏感信息
- 禁用敏感数据日志记录
- 使用最小日志级别
- 启用所有安全特性

### 配置文件示例

#### 开发环境配置
```bash
# .env.development
DATABASE_PASSWORD=dev-password
JWT_SECRET_KEY=dev-jwt-secret-key
SMTP_PASSWORD=dev-smtp-password
```

#### 生产环境配置
```bash
# 环境变量
export DATABASE_PASSWORD="production-password"
export JWT_SECRET_KEY="production-jwt-secret-key"
export SMTP_PASSWORD="production-smtp-password"
```

## 安全最佳实践

### 1. 敏感信息管理

- **永远不要**将敏感信息提交到版本控制
- 使用环境变量或用户机密存储敏感信息
- 生产环境使用密钥管理服务（如 Azure Key Vault）

### 2. 配置验证

项目包含配置验证器，在应用启动时验证配置完整性：

```csharp
// 在 Program.cs 中启用配置验证
services.ValidateConfiguration(configuration);
```

### 3. 连接字符串管理

- 数据库连接字符串统一管理
- Orleans 组件自动继承数据库连接字符串
- 支持独立配置各组件连接字符串

## 配置扩展方法

项目提供了便捷的配置扩展方法：

```csharp
// 获取数据库连接字符串
var dbConnectionString = configuration.GetDatabaseConnectionString();

// 获取 Orleans 连接字符串
var clusteringConnectionString = configuration.GetOrleansConnectionString("clustering");
var storageConnectionString = configuration.GetOrleansConnectionString("storage");

// 获取 Kafka 配置
var kafkaBrokers = configuration.GetKafkaBrokersString();
```

## 部署配置

### Docker 部署

使用环境变量文件：

```bash
# 创建 .env 文件
cp .env.example .env
# 编辑 .env 文件填入实际值

# 启动服务
docker-compose up -d
```

### Kubernetes 部署

使用 ConfigMap 和 Secret：

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: curio-secrets
data:
  database-password: <base64-encoded-password>
  jwt-secret-key: <base64-encoded-key>
```

## 故障排除

### 常见配置问题

1. **数据库连接失败**
   - 检查连接字符串格式
   - 验证数据库服务是否运行
   - 确认网络连接

2. **Orleans 集群连接问题**
   - 检查 Orleans 配置节点名称
   - 验证数据库表是否正确创建
   - 确认集群 ID 和服务 ID 配置

3. **Kafka 连接问题**
   - 检查 Kafka 服务状态
   - 验证主题是否存在
   - 确认安全配置

### 配置验证

使用内置的配置验证器检查配置问题：

```bash
# 应用启动时会自动验证配置
# 查看日志输出获取验证结果
```

## 配置迁移

从旧配置迁移到新配置结构：

1. 备份现有配置文件
2. 使用配置模板创建新配置
3. 迁移敏感信息到环境变量
4. 测试配置验证
5. 部署新配置

---

更多详细信息请参考项目源码中的配置类定义。
