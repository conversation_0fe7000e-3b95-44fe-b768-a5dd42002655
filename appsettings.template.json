{"_comment": "Curio API Local Configuration Template - Copy this file to appsettings.Local.json and customize for your local environment", "Application": {"Environment": "Development", "Api": {"BaseUrl": "https://localhost:7274", "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:7274"]}}, "Security": {"Jwt": {"SecretKey": "your-local-jwt-secret-key-change-this-32-characters-minimum", "Issuer": "Curio.Local", "Audience": "Curio.Api.Local"}, "Encryption": {"Key": "your-local-encryption-key-32-chars", "Salt": "your-local-salt-16"}}}, "Database": {"Host": "localhost", "Port": 5432, "Database": "curio", "Username": "curio", "Password": "your-local-database-password"}, "Orleans": {"ClusterId": "curio-cluster-local", "ServiceId": "curio-service-local"}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams-local", "SecurityProtocol": "PLAINTEXT"}, "Email": {"Smtp": {"Host": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "your-app-password", "EnableSsl": true}, "DefaultSender": {"FromEmail": "<EMAIL>", "FromName": "Curio Local Dev"}}}