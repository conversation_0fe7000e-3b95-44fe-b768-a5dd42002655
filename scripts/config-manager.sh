#!/bin/bash

# Curio API Configuration Manager
# 用于管理 appsettings.json 配置文件

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

echo "🔧 Curio API Configuration Manager"
echo "=================================="

# 显示当前配置状态
show_current_config() {
    echo "📋 Current configuration files:"

    local files=("appsettings.json" "appsettings.Development.json" "appsettings.Local.json" "appsettings.template.json")
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            echo "  ✅ $file (exists)"
        else
            echo "  ❌ $file (missing)"
        fi
    done

    echo ""
    echo "🔍 Current environment: ${ASPNETCORE_ENVIRONMENT:-Development}"
    echo ""
}

# 备份当前配置
backup_config() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="config-backups/$timestamp"

    echo "💾 Creating backup in $backup_dir..."
    mkdir -p "$backup_dir"

    local files=("appsettings.json" "appsettings.Development.json" "appsettings.Local.json")
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            cp "$file" "$backup_dir/"
            echo "  ✅ Backed up $file"
        fi
    done

    echo "✅ Backup completed: $backup_dir"
    echo ""
}

# 初始化本地配置
init_local_config() {
    if [ -f "appsettings.Local.json" ]; then
        echo "⚠️  appsettings.Local.json already exists"
        read -p "Do you want to overwrite it? (y/N): " confirm
        if [[ ! $confirm =~ ^[Yy]$ ]]; then
            echo "❌ Cancelled"
            return 0
        fi
    fi

    if [ ! -f "appsettings.template.json" ]; then
        echo "❌ appsettings.template.json not found"
        return 1
    fi

    echo "📝 Creating appsettings.Local.json from template..."
    cp "appsettings.template.json" "appsettings.Local.json"
    echo "✅ appsettings.Local.json created"
    echo ""
    echo "🔧 Please edit appsettings.Local.json to customize your local settings:"
    echo "   - Database password"
    echo "   - JWT secret key"
    echo "   - Email configuration (if needed)"
    echo "   - Kafka brokers (if different)"
    echo ""
}

# 恢复配置
restore_config() {
    echo "📂 Available backups:"
    if [ -d "config-backups" ]; then
        ls -1 config-backups/ | nl
    else
        echo "  No backups found"
        return 1
    fi
    
    echo ""
    read -p "Enter backup number to restore (or 'q' to quit): " choice
    
    if [ "$choice" = "q" ]; then
        return 0
    fi
    
    local backup_name=$(ls -1 config-backups/ | sed -n "${choice}p")
    if [ -z "$backup_name" ]; then
        echo "❌ Invalid backup number"
        return 1
    fi
    
    local backup_dir="config-backups/$backup_name"
    echo "🔄 Restoring from $backup_dir..."
    
    local files=(".env" ".env.development" ".env.local" ".env.development.local")
    for file in "${files[@]}"; do
        if [ -f "$backup_dir/$file" ]; then
            cp "$backup_dir/$file" .
            echo "  ✅ Restored $file"
        fi
    done
    
    echo "✅ Configuration restored"
    echo ""
}

# 创建配置模板
create_template() {
    local template_name="$1"
    
    if [ -z "$template_name" ]; then
        read -p "Enter template name: " template_name
    fi
    
    local template_dir="config-templates/$template_name"
    mkdir -p "$template_dir"
    
    echo "📝 Creating template '$template_name'..."
    
    local files=(".env" ".env.development" ".env.local" ".env.development.local")
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            cp "$file" "$template_dir/"
            echo "  ✅ Saved $file to template"
        fi
    done
    
    echo "✅ Template '$template_name' created"
    echo ""
}

# 应用配置模板
apply_template() {
    echo "📂 Available templates:"
    if [ -d "config-templates" ]; then
        ls -1 config-templates/ | nl
    else
        echo "  No templates found"
        return 1
    fi
    
    echo ""
    read -p "Enter template number to apply (or 'q' to quit): " choice
    
    if [ "$choice" = "q" ]; then
        return 0
    fi
    
    local template_name=$(ls -1 config-templates/ | sed -n "${choice}p")
    if [ -z "$template_name" ]; then
        echo "❌ Invalid template number"
        return 1
    fi
    
    # 先备份当前配置
    backup_config
    
    local template_dir="config-templates/$template_name"
    echo "🔄 Applying template '$template_name'..."
    
    local files=(".env" ".env.development" ".env.local" ".env.development.local")
    for file in "${files[@]}"; do
        if [ -f "$template_dir/$file" ]; then
            cp "$template_dir/$file" .
            echo "  ✅ Applied $file"
        fi
    done
    
    echo "✅ Template '$template_name' applied"
    echo ""
}

# 运行应用并显示配置
run_with_config() {
    local env="${1:-Development}"
    
    echo "🚀 Running application with environment: $env"
    echo "📋 Configuration summary will be displayed on startup..."
    echo ""
    
    ASPNETCORE_ENVIRONMENT="$env" dotnet run --project src/Curio.Api
}

# 显示菜单
show_menu() {
    echo "🎯 What would you like to do?"
    echo ""
    echo "1) Show current configuration status"
    echo "2) Initialize local configuration (appsettings.Local.json)"
    echo "3) Run application (Development)"
    echo "4) Run application (Production)"
    echo "5) Run application (Testing)"
    echo "6) Backup current configuration"
    echo "7) Restore configuration from backup"
    echo "8) Create configuration template"
    echo "9) Apply configuration template"
    echo "10) Edit appsettings.Local.json"
    echo "0) Exit"
    echo ""
}

# 主菜单循环
main_menu() {
    while true; do
        show_menu
        read -p "Enter your choice (0-9): " choice
        echo ""
        
        case $choice in
            1)
                show_current_config
                ;;
            2)
                init_local_config
                ;;
            3)
                run_with_config "Development"
                ;;
            4)
                run_with_config "Production"
                ;;
            5)
                run_with_config "Testing"
                ;;
            6)
                backup_config
                ;;
            7)
                restore_config
                ;;
            8)
                create_template
                ;;
            9)
                apply_template
                ;;
            10)
                if [ ! -f "appsettings.Local.json" ]; then
                    init_local_config
                fi
                ${EDITOR:-nano} "appsettings.Local.json"
                ;;
            0)
                echo "👋 Goodbye!"
                exit 0
                ;;
            *)
                echo "❌ Invalid choice. Please try again."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
        echo ""
    done
}

# 如果有命令行参数，直接执行
if [ $# -gt 0 ]; then
    case "$1" in
        "status")
            show_current_config
            ;;
        "run")
            run_with_config "${2:-Development}"
            ;;
        "backup")
            backup_config
            ;;
        "template")
            if [ "$2" = "create" ]; then
                create_template "$3"
            elif [ "$2" = "apply" ]; then
                apply_template
            else
                echo "Usage: $0 template [create|apply] [name]"
            fi
            ;;
        *)
            echo "Usage: $0 [status|run|backup|template]"
            echo "  status                 - Show configuration status"
            echo "  run [env]             - Run application with environment"
            echo "  backup                - Backup current configuration"
            echo "  template create [name] - Create configuration template"
            echo "  template apply        - Apply configuration template"
            ;;
    esac
else
    # 显示当前状态
    show_current_config
    
    # 进入交互菜单
    main_menu
fi
