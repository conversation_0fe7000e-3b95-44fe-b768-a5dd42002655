using Orleans;
using Orleans.Streams;
using Microsoft.Extensions.Logging;
using Curio.Orleans.Interfaces.Email;
using Curio.Orleans.Interfaces.Users;
using Curio.Infrastructure.Services;
using Curio.Application.Interfaces;
using Curio.Shared.Users;

namespace Curio.Orleans.Grains.Email;

/// <summary>
/// Orleans Grain for consuming email events and handling asynchronous email delivery
/// </summary>
public class EmailConsumerGrain : Grain<EmailConsumerState>, IEmailConsumerGrain
{
    private readonly ISmtpEmailService _emailService;
    private readonly ILogger<EmailConsumerGrain> _logger;
    private IAsyncStream<DomainEvent>? _emailEventStream;

    public EmailConsumerGrain(ISmtpEmailService emailService, ILogger<EmailConsumerGrain> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);

        var streamProvider = this.GetStreamProvider("KafkaStreams");
        _emailEventStream = streamProvider.GetStream<DomainEvent>("email-events", this.GetPrimaryKeyString());

        _logger.LogInformation("EmailConsumerGrain {GrainId} activated", this.GetPrimaryKeyString());
    }

    public async Task ProcessVerificationCodeEventAsync(VerificationCodeGeneratedEvent @event)
    {
        var eventId = @event.EventId;
        
        _logger.LogInformation("Processing verification code event {EventId} for email {Email}", 
            eventId, @event.Email);

        // Check if this event has already been processed
        if (State.ProcessedEvents.ContainsKey(eventId))
        {
            _logger.LogInformation("Event {EventId} already processed, skipping", eventId);
            return;
        }

        // Create email sending record
        var emailResult = new EmailSendingResult
        {
            EventId = eventId,
            Email = @event.Email,
            Purpose = @event.Purpose,
            Status = EmailSendingStatus.Processing,
            ProcessedAt = DateTime.UtcNow,
            AttemptCount = 1
        };

        State.ProcessedEvents[eventId] = emailResult;
        State.Stats.TotalProcessed++;
        State.Stats.Pending++;
        State.Stats.LastProcessedAt = DateTime.UtcNow;
        await WriteStateAsync();

        // Publish email sending initiated event
        var initiatedEvent = new EmailSendingInitiatedEvent
        {
            Email = @event.Email,
            Purpose = @event.Purpose,
            VerificationEventId = eventId,
            InitiatedAt = DateTime.UtcNow
        };

        if (_emailEventStream != null)
        {
            await _emailEventStream.OnNextAsync(initiatedEvent);
        }

        // Attempt to send email with retry logic
        await TrySendEmailAsync(@event, emailResult);
    }

    private async Task TrySendEmailAsync(VerificationCodeGeneratedEvent @event, EmailSendingResult emailResult)
    {
        const int maxRetries = 3;
        var delays = new[] { 1000, 2000, 4000 }; // Exponential backoff

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                emailResult.AttemptCount = attempt;
                emailResult.Status = EmailSendingStatus.Processing;
                await WriteStateAsync();

                // Get the verification code from the VerificationGrain
                var verificationGrain = GrainFactory.GetGrain<IVerificationGrain>(@event.Email);
                var code = await verificationGrain.GetValidCodeAsync(@event.Purpose);

                if (string.IsNullOrEmpty(code))
                {
                    throw new InvalidOperationException("Verification code not found or expired");
                }

                // Send the email using template
                var subject = @event.Purpose switch
                {
                    "registration" => "欢迎注册 Curio - 验证码",
                    "login" => "Curio 登录验证码",
                    _ => "Curio 验证码"
                };

                var templateModel = new
                {
                    code = code,
                    purpose = @event.Purpose,
                    expiresAt = @event.ExpiresAt,
                    email = @event.Email
                };

                var success = await _emailService.SendTemplatedEmailAsync("verification-code", @event.Email, subject, templateModel);

                if (success)
                {
                    // Email sent successfully
                    emailResult.Status = EmailSendingStatus.Sent;
                    State.Stats.SuccessfulSent++;
                    State.Stats.Pending--;
                    await WriteStateAsync();

                    // Publish success event
                    var successEvent = new EmailSentSuccessfullyEvent
                    {
                        Email = @event.Email,
                        Purpose = @event.Purpose,
                        VerificationEventId = @event.EventId,
                        SentAt = DateTime.UtcNow,
                        AttemptCount = attempt
                    };

                    if (_emailEventStream != null)
                    {
                        await _emailEventStream.OnNextAsync(successEvent);
                    }

                    _logger.LogInformation("Email sent successfully to {Email} for {Purpose} after {Attempts} attempts", 
                        @event.Email, @event.Purpose, attempt);
                    return;
                }
                else
                {
                    throw new InvalidOperationException("Email service returned false");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Email sending attempt {Attempt}/{MaxAttempts} failed for {Email}: {Message}", 
                    attempt, maxRetries, @event.Email, ex.Message);

                if (attempt == maxRetries)
                {
                    // Final failure
                    emailResult.Status = EmailSendingStatus.Failed;
                    emailResult.ErrorMessage = ex.Message;
                    State.Stats.Failed++;
                    State.Stats.Pending--;
                    await WriteStateAsync();

                    // Publish failure event
                    var failureEvent = new EmailSendingFailedEvent
                    {
                        Email = @event.Email,
                        Purpose = @event.Purpose,
                        VerificationEventId = @event.EventId,
                        ErrorMessage = ex.Message,
                        FailedAt = DateTime.UtcNow,
                        AttemptCount = attempt,
                        WillRetry = false
                    };

                    if (_emailEventStream != null)
                    {
                        await _emailEventStream.OnNextAsync(failureEvent);
                    }

                    _logger.LogError(ex, "Email sending failed permanently for {Email} after {Attempts} attempts", 
                        @event.Email, maxRetries);
                }
                else
                {
                    // Retry with delay
                    emailResult.Status = EmailSendingStatus.Retrying;
                    await WriteStateAsync();

                    // Publish retry event
                    var retryEvent = new EmailSendingFailedEvent
                    {
                        Email = @event.Email,
                        Purpose = @event.Purpose,
                        VerificationEventId = @event.EventId,
                        ErrorMessage = ex.Message,
                        FailedAt = DateTime.UtcNow,
                        AttemptCount = attempt,
                        WillRetry = true
                    };

                    if (_emailEventStream != null)
                    {
                        await _emailEventStream.OnNextAsync(retryEvent);
                    }

                    await Task.Delay(delays[attempt - 1]);
                }
            }
        }
    }

    public async Task<EmailSendingResult?> GetEmailSendingStatusAsync(string eventId)
    {
        await Task.CompletedTask;
        return State.ProcessedEvents.TryGetValue(eventId, out var result) ? result : null;
    }

    public async Task<EmailSendingStats> GetSendingStatsAsync()
    {
        await Task.CompletedTask;
        return State.Stats;
    }
}

/// <summary>
/// State for EmailConsumerGrain
/// </summary>
public class EmailConsumerState
{
    public Dictionary<string, EmailSendingResult> ProcessedEvents { get; set; } = new();
    public EmailSendingStats Stats { get; set; } = new();
}