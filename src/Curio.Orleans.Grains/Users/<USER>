using Orleans;
using Orleans.Streams;
using Curio.Orleans.Interfaces.Users;
using Curio.Orleans.Interfaces.Email;
using Curio.Shared.Users;

namespace Curio.Orleans.Grains.Users;

public class VerificationState
{
    public Dictionary<string, (string Code, DateTime ExpiresAt)> Codes { get; set; } = new();
}

public class VerificationGrain : Grain<VerificationState>, IVerificationGrain
{
    private IAsyncStream<DomainEvent>? _stream;

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);
        
        var streamProvider = this.GetStreamProvider("KafkaStreams");
        _stream = streamProvider.GetStream<DomainEvent>("verification-events", this.GetPrimaryKeyString());
    }

    public async Task<bool> SendVerificationCodeAsync(SendVerificationCodeCommand command)
    {
        // Generate 6-digit verification code
        var code = GenerateVerificationCode();
        var expiresAt = DateTime.UtcNow.AddMinutes(10); // 10 minutes expiration

        // Store the code
        State.Codes[command.Purpose] = (code, expiresAt);
        await WriteStateAsync();

        // Create and publish event
        var codeGeneratedEvent = new VerificationCodeGeneratedEvent
        {
            Email = command.Email,
            Code = code,
            ExpiresAt = expiresAt,
            Purpose = command.Purpose,
            CommandId = command.CommandId
        };

        if (_stream != null)
        {
            await _stream.OnNextAsync(codeGeneratedEvent);
        }

        // Trigger asynchronous email sending via EmailConsumerGrain
        var emailConsumerGrain = GrainFactory.GetGrain<IEmailConsumerGrain>(command.Email);
        _ = emailConsumerGrain.ProcessVerificationCodeEventAsync(codeGeneratedEvent); // Fire and forget

        return true;
    }

    public async Task<bool> VerifyCodeAsync(string code, string purpose)
    {
        await Task.CompletedTask;

        if (!State.Codes.TryGetValue(purpose, out var storedCode))
        {
            return false;
        }

        // Check if code is expired
        if (DateTime.UtcNow > storedCode.ExpiresAt)
        {
            State.Codes.Remove(purpose);
            await WriteStateAsync();
            return false;
        }

        // Check if code matches
        if (storedCode.Code != code)
        {
            return false;
        }

        // Remove the used code
        State.Codes.Remove(purpose);
        await WriteStateAsync();

        return true;
    }

    public async Task<string?> GetValidCodeAsync(string purpose)
    {
        await Task.CompletedTask;

        if (!State.Codes.TryGetValue(purpose, out var storedCode))
        {
            return null;
        }

        // Check if code is expired
        if (DateTime.UtcNow > storedCode.ExpiresAt)
        {
            State.Codes.Remove(purpose);
            await WriteStateAsync();
            return null;
        }

        return storedCode.Code;
    }

    private static string GenerateVerificationCode()
    {
        var random = new Random();
        return random.Next(100000, 999999).ToString();
    }
}