using Orleans;
using Orleans.Streams;
using Microsoft.Extensions.Logging;
using Curio.Orleans.Interfaces.Projection;
using Curio.Shared.Users;

namespace Curio.Orleans.Grains.Projection;

/// <summary>
/// 投影重建Grain实现
/// 支持从Kafka任意时间点重建投影
/// </summary>
public class ProjectionRebuildGrain : Grain, IProjectionRebuildGrain
{
    private readonly ILogger<ProjectionRebuildGrain> _logger;
    private RebuildStatus _status = new();
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _rebuildTask;

    public ProjectionRebuildGrain(ILogger<ProjectionRebuildGrain> logger)
    {
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _status.RebuildId = this.GetPrimaryKeyString();
        _status.Status = "Pending";
        _logger.LogInformation("ProjectionRebuildGrain activated: {RebuildId}", _status.RebuildId);
        await base.OnActivateAsync(cancellationToken);
    }

    public async Task StartRebuildAsync(RebuildRequest request)
    {
        if (_status.Status == "Running")
        {
            throw new InvalidOperationException($"Rebuild {_status.RebuildId} is already running");
        }

        _status = new RebuildStatus
        {
            RebuildId = this.GetPrimaryKeyString(),
            ProjectionName = request.ProjectionName,
            Status = "Running",
            StartedAt = DateTime.UtcNow,
            ProcessedEvents = 0,
            TotalEstimatedEvents = 0,
            ProgressPercentage = 0
        };

        _logger.LogInformation("Starting projection rebuild: {ProjectionName}, FromDate: {FromDate}", 
            request.ProjectionName, request.FromDate);

        // 创建取消令牌
        _cancellationTokenSource = new CancellationTokenSource();

        // 启动重建任务
        _rebuildTask = Task.Run(async () => await ExecuteRebuildAsync(request, _cancellationTokenSource.Token));

        await Task.CompletedTask;
    }

    public Task<RebuildStatus> GetRebuildStatusAsync()
    {
        return Task.FromResult(_status);
    }

    public async Task CancelRebuildAsync()
    {
        if (_status.Status != "Running")
        {
            throw new InvalidOperationException($"Rebuild {_status.RebuildId} is not running");
        }

        _logger.LogWarning("Cancelling projection rebuild: {RebuildId}", _status.RebuildId);

        _cancellationTokenSource?.Cancel();
        _status.Status = "Cancelled";
        _status.CompletedAt = DateTime.UtcNow;

        await Task.CompletedTask;
    }

    public async Task PauseRebuildAsync()
    {
        if (_status.Status != "Running")
        {
            throw new InvalidOperationException($"Rebuild {_status.RebuildId} is not running");
        }

        _status.Status = "Paused";
        _logger.LogInformation("Paused projection rebuild: {RebuildId}", _status.RebuildId);

        await Task.CompletedTask;
    }

    public async Task ResumeRebuildAsync()
    {
        if (_status.Status != "Paused")
        {
            throw new InvalidOperationException($"Rebuild {_status.RebuildId} is not paused");
        }

        _status.Status = "Running";
        _logger.LogInformation("Resumed projection rebuild: {RebuildId}", _status.RebuildId);

        await Task.CompletedTask;
    }

    private async Task ExecuteRebuildAsync(RebuildRequest request, CancellationToken cancellationToken)
    {
        try
        {
            var streamProvider = this.GetStreamProvider("KafkaStreams");

            // 使用专用的重建Topic，或者从主要事件Topic读取
            var rebuildStream = streamProvider.GetStream<DomainEvent>("domain-events", request.ProjectionName);

            var eventCount = 0L;
            var startTime = DateTime.UtcNow;

            // 订阅事件流，从指定时间点开始
            await rebuildStream.SubscribeAsync(async (domainEvent, token) =>
            {
                // 检查取消令牌
                if (cancellationToken.IsCancellationRequested)
                {
                    return;
                }

                // 检查暂停状态
                while (_status.Status == "Paused" && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, cancellationToken);
                }

                // 过滤时间范围
                if (domainEvent.Timestamp < request.FromDate)
                {
                    return;
                }

                if (request.ToDate.HasValue && domainEvent.Timestamp > request.ToDate.Value)
                {
                    return;
                }

                try
                {
                    // 处理事件重建投影
                    await ProcessEventForProjectionAsync(domainEvent, request.ProjectionName);

                    // 更新进度
                    eventCount++;
                    _status.ProcessedEvents = eventCount;

                    // 计算进度和剩余时间（简单估算）
                    var elapsed = DateTime.UtcNow - startTime;
                    if (elapsed.TotalSeconds > 10) // 10秒后开始估算
                    {
                        var eventsPerSecond = eventCount / elapsed.TotalSeconds;
                        _status.ProgressPercentage = Math.Min(95, (eventCount * 100.0) / Math.Max(eventCount, 1000));
                        
                        if (eventsPerSecond > 0)
                        {
                            var remainingEvents = Math.Max(0, _status.TotalEstimatedEvents - eventCount);
                            _status.EstimatedTimeRemaining = TimeSpan.FromSeconds(remainingEvents / eventsPerSecond);
                        }
                    }

                    // 每100个事件记录一次日志
                    if (eventCount % 100 == 0)
                    {
                        _logger.LogDebug("Processed {EventCount} events for projection {ProjectionName}", 
                            eventCount, request.ProjectionName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process event {EventId} for projection {ProjectionName}", 
                        domainEvent.EventId, request.ProjectionName);
                    // 继续处理其他事件，不中断重建过程
                }
            });

            // 重建完成
            if (!cancellationToken.IsCancellationRequested)
            {
                _status.Status = "Completed";
                _status.CompletedAt = DateTime.UtcNow;
                _status.ProgressPercentage = 100;
                _status.EstimatedTimeRemaining = TimeSpan.Zero;

                _logger.LogInformation("Projection rebuild completed: {ProjectionName}, ProcessedEvents: {ProcessedEvents}, Duration: {Duration}",
                    request.ProjectionName, _status.ProcessedEvents, _status.CompletedAt - _status.StartedAt);
            }
        }
        catch (OperationCanceledException)
        {
            _status.Status = "Cancelled";
            _status.CompletedAt = DateTime.UtcNow;
            _logger.LogWarning("Projection rebuild cancelled: {ProjectionName}", request.ProjectionName);
        }
        catch (Exception ex)
        {
            _status.Status = "Failed";
            _status.CompletedAt = DateTime.UtcNow;
            _status.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Projection rebuild failed: {ProjectionName}", request.ProjectionName);
        }
    }

    private async Task ProcessEventForProjectionAsync(DomainEvent domainEvent, string projectionName)
    {
        // 这里实现具体的投影重建逻辑
        // 根据事件类型分发到不同的处理器
        switch (domainEvent)
        {
            case UserRegisteredEvent userRegistered:
                await ProcessUserRegisteredEventAsync(userRegistered, projectionName);
                break;
            case UserLoginAttemptedEvent loginAttempted:
                await ProcessUserLoginAttemptedEventAsync(loginAttempted, projectionName);
                break;
            // 添加其他事件类型的处理
            default:
                _logger.LogDebug("Unhandled event type for projection rebuild: {EventType}", domainEvent.GetType().Name);
                break;
        }
    }

    private async Task ProcessUserRegisteredEventAsync(UserRegisteredEvent @event, string projectionName)
    {
        // 实现用户注册事件的投影重建逻辑
        _logger.LogDebug("Processing UserRegisteredEvent for projection {ProjectionName}: {UserId}", 
            projectionName, @event.UserId);
        
        // TODO: 调用具体的投影更新逻辑
        await Task.CompletedTask;
    }

    private async Task ProcessUserLoginAttemptedEventAsync(UserLoginAttemptedEvent @event, string projectionName)
    {
        // 实现用户登录事件的投影重建逻辑
        _logger.LogDebug("Processing UserLoginAttemptedEvent for projection {ProjectionName}: {Email}", 
            projectionName, @event.Email);
        
        // TODO: 调用具体的投影更新逻辑
        await Task.CompletedTask;
    }
}