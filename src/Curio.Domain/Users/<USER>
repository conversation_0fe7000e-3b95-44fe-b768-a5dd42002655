using Curio.Shared.Users;

namespace Curio.Domain.Users;

// 用户聚合状态
public class UserState
{
    public string Id { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public UserStatus Status { get; private set; } = UserStatus.PendingVerification;
    public DateTime RegisteredAt { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public HashSet<string> ProcessedCommands { get; private set; } = new();

    // 业务规则验证
    public bool CanRegister(string email, string name)
    {
        if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(name))
            return false;
            
        if (!IsValidEmail(email))
            return false;
            
        // 如果用户已经存在且已验证，不能重新注册
        if (!string.IsNullOrEmpty(Id) && Status != UserStatus.PendingVerification)
            return false;
            
        return true;
    }
    
    public bool IsVerified => Status == UserStatus.Active && VerifiedAt.HasValue;
    
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    // 状态变更方法（由事件驱动）
    public void ApplyEvent(UserRegistrationInitiatedEvent @event)
    {
        // 注册初始化时不改变状态，等待验证码验证
        ProcessedCommands.Add(@event.CommandId);
    }

    public void ApplyEvent(UserRegisteredEvent @event)
    {
        Id = @event.UserId;
        Email = @event.Email;
        Name = @event.Name;
        Status = UserStatus.Active;
        RegisteredAt = @event.RegisteredAt;
        VerifiedAt = @event.RegisteredAt;
        ProcessedCommands.Add(@event.CommandId);
    }

    public void ApplyEvent(UserLoginAttemptedEvent @event)
    {
        ProcessedCommands.Add(@event.CommandId);
    }

    public UserDto ToDto()
    {
        return new UserDto
        {
            Id = Id,
            Email = Email,
            Name = Name,
            RegisteredAt = RegisteredAt,
            IsVerified = IsVerified
        };
    }
}