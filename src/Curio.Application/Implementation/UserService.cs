using Orleans;
using Curio.Application.Interfaces;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Users;

namespace Curio.Application.Implementation;

public class UserService : IUserService
{
    private readonly IGrainFactory _grainFactory;

    public UserService(IGrainFactory grainFactory)
    {
        _grainFactory = grainFactory;
    }

    public async Task<EmailExistsResult> CheckEmailExistsAsync(CheckEmailExistsCommand command)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(command.Email);
        return await userGrain.CheckEmailExistsAsync(command);
    }

    public async Task<bool> SendVerificationCodeAsync(SendVerificationCodeCommand command)
    {
        var verificationGrain = _grainFactory.GetGrain<IVerificationGrain>(command.Email);
        return await verificationGrain.SendVerificationCodeAsync(command);
    }

    public async Task<VerificationResult> RegisterUserAsync(RegisterUserCommand command)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(command.Email);
        return await userGrain.RegisterUserAsync(command);
    }

    public async Task<VerificationResult> LoginUserAsync(LoginUserCommand command)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(command.Email);
        return await userGrain.LoginUserAsync(command);
    }

    public async Task<UserDto?> GetUserAsync(string email)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(email);
        return await userGrain.GetUserAsync();
    }
}