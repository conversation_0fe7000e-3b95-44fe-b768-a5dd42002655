using Orleans;
using Curio.Application.Interfaces;
using Curio.Orleans.Interfaces.Email;

namespace Curio.Application.Implementation;

/// <summary>
/// Application service implementation for email status operations
/// </summary>
public class EmailStatusService : IEmailStatusService
{
    private readonly IGrainFactory _grainFactory;

    public EmailStatusService(IGrainFactory grainFactory)
    {
        _grainFactory = grainFactory;
    }

    public async Task<EmailSendingResult?> GetEmailSendingStatusAsync(string email, string eventId)
    {
        var emailConsumerGrain = _grainFactory.GetGrain<IEmailConsumerGrain>(email);
        return await emailConsumerGrain.GetEmailSendingStatusAsync(eventId);
    }

    public async Task<EmailSendingStats> GetEmailSendingStatsAsync(string email)
    {
        var emailConsumerGrain = _grainFactory.GetGrain<IEmailConsumerGrain>(email);
        return await emailConsumerGrain.GetSendingStatsAsync();
    }
}