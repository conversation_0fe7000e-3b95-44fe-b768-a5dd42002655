using Curio.Orleans.Interfaces.Email;

namespace Curio.Application.Interfaces;

/// <summary>
/// Application service interface for email-related operations
/// </summary>
public interface IEmailStatusService
{
    /// <summary>
    /// Get email sending status for a specific verification event
    /// </summary>
    /// <param name="email">The email address</param>
    /// <param name="eventId">The verification event ID</param>
    /// <returns>Email sending result or null if not found</returns>
    Task<EmailSendingResult?> GetEmailSendingStatusAsync(string email, string eventId);

    /// <summary>
    /// Get email sending statistics for a specific email address
    /// </summary>
    /// <param name="email">The email address</param>
    /// <returns>Email sending statistics</returns>
    Task<EmailSendingStats> GetEmailSendingStatsAsync(string email);
}