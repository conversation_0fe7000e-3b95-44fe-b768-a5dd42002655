namespace Curio.Application.Interfaces;

public interface IEmailTemplateService
{
    Task<string> RenderTemplateAsync(string templateName, object model);
    Task<bool> TemplateExistsAsync(string templateName);
}

public interface ISmtpEmailService
{
    Task<bool> SendEmailAsync(EmailMessage message);
    Task<bool> SendTemplatedEmailAsync(string templateName, string to, string subject, object templateModel);
    Task<bool> SendBulkEmailAsync(IEnumerable<EmailMessage> messages);
}

public class EmailMessage
{
    public string To { get; set; } = string.Empty;
    public string? ToName { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string? PlainTextBody { get; set; }
    public string? HtmlBody { get; set; }
    public string? From { get; set; }
    public string? FromName { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public List<EmailAttachment> Attachments { get; set; } = new();
}

public class EmailAttachment
{
    public string FileName { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = "application/octet-stream";
}

public class EmailTemplate
{
    public string Name { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string? PlainTextTemplate { get; set; }
    public string? HtmlTemplate { get; set; }
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> DefaultVariables { get; set; } = new();
}