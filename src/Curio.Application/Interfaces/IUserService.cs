using Curio.Shared.Users;

namespace Curio.Application.Interfaces;

public interface IUserService
{
    Task<EmailExistsResult> CheckEmailExistsAsync(CheckEmailExistsCommand command);
    Task<bool> SendVerificationCodeAsync(SendVerificationCodeCommand command);
    Task<VerificationResult> RegisterUserAsync(RegisterUserCommand command);
    Task<VerificationResult> LoginUserAsync(LoginUserCommand command);
    Task<UserDto?> GetUserAsync(string email);
}