using Orleans;
using Curio.Shared.Users;

namespace Curio.Orleans.Interfaces.Users;

public interface IUserGrain : IGrainWithStringKey
{
    Task<EmailExistsResult> CheckEmailExistsAsync(CheckEmailExistsCommand command);
    Task<VerificationResult> RegisterUserAsync(RegisterUserCommand command);
    Task<VerificationResult> LoginUserAsync(LoginUserCommand command);
    Task<UserDto?> GetUserAsync();
}