{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"Development": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:11111"}, "Local": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:11111"}, "Production": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production", "DOTNET_ENVIRONMENT": "Production"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:11111"}}}