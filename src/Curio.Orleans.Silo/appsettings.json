{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Orleans": "Information", "Microsoft.Orleans": "Warning"}}, "Application": {"Name": "Curio Orleans Silo", "Version": "1.0.0", "Environment": "Production"}, "Database": {"ConnectionString": "", "Host": "localhost", "Port": 5432, "Database": "curio", "Username": "curio", "Password": "", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Orleans": {"ClusterId": "curio-cluster", "ServiceId": "curio-service", "Clustering": {"Provider": "AdoNet", "ConnectionString": "", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": ""}, "Reminders": {"Provider": "AdoNet", "ConnectionString": ""}}, "Kafka": {"BrokerList": ["kafka-prod:9092"], "ConsumerGroupId": "orleans-event-streams-prod", "Topics": ["domain-events", "verification-events", "user-events", "dead-letter-queue", "projection-rebuild"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "SecurityProtocol": "SASL_PLAINTEXT", "SaslMechanism": "PLAIN", "SaslUsername": "", "SaslPassword": ""}, "Email": {"Smtp": {"Host": "", "Port": 587, "Username": "", "Password": "", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "", "FromName": "<PERSON><PERSON><PERSON>", "ReplyToEmail": "", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}, "ResilientPublish": {"MaxRetries": 5, "BaseDelayMs": 1000, "BackupDirectory": "/var/log/curio/failed-events", "EnableDeadLetterQueue": true, "EnableLocalBackup": true, "PublishTimeoutSeconds": 30}}