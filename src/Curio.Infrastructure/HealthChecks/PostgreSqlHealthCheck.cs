using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using Curio.Infrastructure.Configuration;

namespace Curio.Infrastructure.HealthChecks;

/// <summary>
/// PostgreSQL健康检查
/// </summary>
public class PostgreSqlHealthCheck : IHealthCheck
{
    private readonly DatabaseSettings _databaseSettings;
    private readonly ILogger<PostgreSqlHealthCheck> _logger;

    public PostgreSqlHealthCheck(
        IOptions<DatabaseSettings> databaseSettings,
        ILogger<PostgreSqlHealthCheck> logger)
    {
        _databaseSettings = databaseSettings.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var connectionString = !string.IsNullOrEmpty(_databaseSettings.ConnectionString)
                ? _databaseSettings.ConnectionString
                : BuildConnectionString();

            using var connection = new NpgsqlConnection(connectionString);
            
            // 设置超时
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(TimeSpan.FromSeconds(10));

            await connection.OpenAsync(cts.Token);

            // 执行简单查询测试连接
            using var command = new NpgsqlCommand("SELECT 1", connection);
            var result = await command.ExecuteScalarAsync(cts.Token);

            // 获取数据库信息
            using var versionCommand = new NpgsqlCommand("SELECT version()", connection);
            var version = await versionCommand.ExecuteScalarAsync(cts.Token) as string;

            // 检查Orleans相关表是否存在
            var orleansTablesExist = await CheckOrleansTablesAsync(connection, cts.Token);

            var data = new Dictionary<string, object>
            {
                ["ConnectionString"] = MaskConnectionString(connectionString),
                ["DatabaseName"] = _databaseSettings.Database,
                ["Host"] = _databaseSettings.Host,
                ["Port"] = _databaseSettings.Port,
                ["PostgreSqlVersion"] = version ?? "Unknown",
                ["OrleansTablesExist"] = orleansTablesExist
            };

            if (!orleansTablesExist)
            {
                _logger.LogWarning("PostgreSQL connection successful but Orleans tables are missing");
                return HealthCheckResult.Degraded(
                    "PostgreSQL connected but Orleans tables are missing",
                    data: data);
            }

            _logger.LogDebug("PostgreSQL health check successful");
            return HealthCheckResult.Healthy(
                "PostgreSQL is healthy",
                data: data);
        }
        catch (OperationCanceledException)
        {
            _logger.LogError("PostgreSQL health check timed out");
            return HealthCheckResult.Unhealthy("PostgreSQL connection timed out");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PostgreSQL health check failed");
            return HealthCheckResult.Unhealthy(
                "PostgreSQL connection failed",
                ex);
        }
    }

    private string BuildConnectionString()
    {
        return $"Host={_databaseSettings.Host};" +
               $"Port={_databaseSettings.Port};" +
               $"Database={_databaseSettings.Database};" +
               $"Username={_databaseSettings.Username};" +
               $"Password={_databaseSettings.Password};" +
               $"CommandTimeout={_databaseSettings.CommandTimeout}";
    }

    private static string MaskConnectionString(string connectionString)
    {
        // 隐藏密码信息
        return System.Text.RegularExpressions.Regex.Replace(
            connectionString,
            @"Password=([^;]+)",
            "Password=***",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);
    }

    private async Task<bool> CheckOrleansTablesAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        try
        {
            // 检查Orleans集群管理表是否存在
            var checkTablesQuery = @"
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('orleansmembershipversioned', 'orleansmembership', 'orleansquery')";

            using var command = new NpgsqlCommand(checkTablesQuery, connection);
            var tableCount = await command.ExecuteScalarAsync(cancellationToken);

            return Convert.ToInt32(tableCount) > 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check Orleans tables existence");
            return false;
        }
    }
}