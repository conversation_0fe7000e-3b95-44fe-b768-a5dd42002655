using Orleans;
using Orleans.Runtime;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Curio.Infrastructure.HealthChecks;

/// <summary>
/// Orleans集群健康检查
/// </summary>
public class OrleansClusterHealthCheck : IHealthCheck
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<OrleansClusterHealthCheck> _logger;

    public OrleansClusterHealthCheck(
        IGrainFactory grainFactory,
        ILogger<OrleansClusterHealthCheck> logger)
    {
        _grainFactory = grainFactory;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取管理Grain来检查集群状态
            var managementGrain = _grainFactory.GetGrain<IManagementGrain>(0);
            
            // 设置超时
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(TimeSpan.FromSeconds(10));

            var hosts = await managementGrain.GetHosts();
            var activeHosts = hosts.Where(h => h.Value == SiloStatus.Active).ToList();

            var data = new Dictionary<string, object>
            {
                ["TotalHosts"] = hosts.Count,
                ["ActiveHosts"] = activeHosts.Count,
                ["Hosts"] = hosts.Select(h => new
                {
                    SiloAddress = h.Key.ToString(),
                    Status = h.Value.ToString(),
                    SiloName = h.Key.ToString() // Orleans不提供SiloName，使用Address代替
                }).ToList()
            };

            if (activeHosts.Count == 0)
            {
                _logger.LogError("No active Orleans silos detected");
                return HealthCheckResult.Unhealthy(
                    "No active Orleans silos",
                    data: data);
            }

            if (activeHosts.Count < 2)
            {
                _logger.LogWarning("Only {ActiveHosts} active silos, cluster may not be resilient", activeHosts.Count);
                return HealthCheckResult.Degraded(
                    $"Only {activeHosts.Count} active silos - cluster may not be resilient",
                    data: data);
            }

            _logger.LogDebug("Orleans cluster healthy: {ActiveHosts} active silos", activeHosts.Count);
            return HealthCheckResult.Healthy(
                $"Orleans cluster healthy with {activeHosts.Count} active silos",
                data: data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check Orleans cluster health");
            return HealthCheckResult.Unhealthy(
                "Failed to check Orleans cluster health",
                ex);
        }
    }
}