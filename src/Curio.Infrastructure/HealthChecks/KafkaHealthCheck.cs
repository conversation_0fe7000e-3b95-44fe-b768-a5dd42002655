using Confluent.Kafka;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Curio.Infrastructure.Configuration;

namespace Curio.Infrastructure.HealthChecks;

/// <summary>
/// Kafka健康检查
/// </summary>
public class KafkaHealthCheck : IHealthCheck
{
    private readonly KafkaSettings _kafkaSettings;
    private readonly ILogger<KafkaHealthCheck> _logger;

    public KafkaHealthCheck(
        IOptions<KafkaSettings> kafkaSettings,
        ILogger<KafkaHealthCheck> logger)
    {
        _kafkaSettings = kafkaSettings.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var config = new AdminClientConfig
            {
                BootstrapServers = string.Join(",", _kafkaSettings.BrokerList)
            };

            using var adminClient = new AdminClientBuilder(config).Build();

            // 设置超时
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(TimeSpan.FromSeconds(10));

            // 获取集群元数据
            var result = await Task.Run(() =>
            {
                var metadata = adminClient.GetMetadata(TimeSpan.FromSeconds(5));
                return ProcessMetadata(metadata);
            }, cts.Token);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check Kafka health");
            return HealthCheckResult.Unhealthy(
                "Failed to connect to Kafka",
                ex);
        }
    }

    private HealthCheckResult ProcessMetadata(Confluent.Kafka.Metadata metadata)
    {
        var brokerInfo = metadata.Brokers.Select(b => new
        {
            b.BrokerId,
            b.Host,
            b.Port
        }).ToList();

        var topicInfo = metadata.Topics
            .Where(t => _kafkaSettings.Topics.Contains(t.Topic))
            .Select(t => new
            {
                t.Topic,
                PartitionCount = t.Partitions.Count,
                t.Error
            }).ToList();

        var data = new Dictionary<string, object>
        {
            ["BrokerCount"] = metadata.Brokers.Count,
            ["Brokers"] = brokerInfo,
            ["Topics"] = topicInfo,
            ["ConfiguredTopics"] = _kafkaSettings.Topics
        };

        // 检查是否有配置的Topic不存在
        var missingTopics = _kafkaSettings.Topics
            .Where(configTopic => !metadata.Topics.Any(t => t.Topic == configTopic))
            .ToList();

        if (missingTopics.Any())
        {
            _logger.LogWarning("Missing Kafka topics: {MissingTopics}", string.Join(", ", missingTopics));
            return HealthCheckResult.Degraded(
                $"Missing topics: {string.Join(", ", missingTopics)}",
                data: data);
        }

        // 检查是否有Topic有错误
        var topicsWithErrors = metadata.Topics
            .Where(t => t.Error != ErrorCode.NoError)
            .ToList();

        if (topicsWithErrors.Any())
        {
            _logger.LogError("Kafka topics with errors: {ErrorTopics}", 
                string.Join(", ", topicsWithErrors.Select(t => $"{t.Topic}:{t.Error}")));
            return HealthCheckResult.Unhealthy(
                $"Topics with errors: {string.Join(", ", topicsWithErrors.Select(t => t.Topic))}",
                data: data);
        }

        _logger.LogDebug("Kafka cluster healthy: {BrokerCount} brokers, {TopicCount} topics", 
            metadata.Brokers.Count, topicInfo.Count);

        return HealthCheckResult.Healthy(
            $"Kafka healthy: {metadata.Brokers.Count} brokers, {topicInfo.Count} topics",
            data: data);
    }
}