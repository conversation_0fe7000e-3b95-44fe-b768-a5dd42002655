namespace Curio.Infrastructure.Configuration;

/// <summary>
/// 弹性事件发布配置选项
/// </summary>
public class ResilientPublishOptions
{
    public const string SectionName = "ResilientPublish";

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 基础延迟时间(毫秒)
    /// </summary>
    public int BaseDelayMs { get; set; } = 1000;

    /// <summary>
    /// 失败事件备份目录
    /// </summary>
    public string BackupDirectory { get; set; } = "failed-events";

    /// <summary>
    /// 是否启用死信队列
    /// </summary>
    public bool EnableDeadLetterQueue { get; set; } = true;

    /// <summary>
    /// 是否启用本地备份
    /// </summary>
    public bool EnableLocalBackup { get; set; } = true;

    /// <summary>
    /// 事件发布超时时间(秒)
    /// </summary>
    public int PublishTimeoutSeconds { get; set; } = 10;
}