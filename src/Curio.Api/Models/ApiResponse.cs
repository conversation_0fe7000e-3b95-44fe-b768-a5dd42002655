using System.Text.Json.Serialization;

namespace Curio.Api.Models;

/// <summary>
/// 统一的 API 响应格式
/// </summary>
/// <typeparam name="T">响应数据类型</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// 操作是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 业务状态码
    /// </summary>
    [JsonPropertyName("code")]
    public int Code { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 响应数据
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    public static ApiResponse<T> CreateSuccess(T data, string message = "Success", int code = 20000)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Code = code,
            Message = message,
            Data = data
        };
    }

    /// <summary>
    /// 创建失败响应
    /// </summary>
    public static ApiResponse<T> CreateError(string message, int code, T? data = default)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Code = code,
            Message = message,
            Data = data
        };
    }
}

/// <summary>
/// 无数据的 API 响应格式
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    /// <summary>
    /// 创建成功响应（无数据）
    /// </summary>
    public static ApiResponse CreateSuccess(string message = "Success", int code = 20000)
    {
        return new ApiResponse
        {
            Success = true,
            Code = code,
            Message = message,
            Data = null
        };
    }

    /// <summary>
    /// 创建失败响应（无数据）
    /// </summary>
    public static ApiResponse CreateError(string message, int code)
    {
        return new ApiResponse
        {
            Success = false,
            Code = code,
            Message = message,
            Data = null
        };
    }
}