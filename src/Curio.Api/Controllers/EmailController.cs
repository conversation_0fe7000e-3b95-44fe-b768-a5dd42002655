using Microsoft.AspNetCore.Mvc;
using Curio.Application.Interfaces;
using Curio.Orleans.Interfaces.Email;
using Curio.Api.Models;

namespace Curio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class EmailController : BaseController
{
    private readonly IEmailStatusService _emailStatusService;
    private readonly ILogger<EmailController> _logger;

    public EmailController(IEmailStatusService emailStatusService, ILogger<EmailController> logger)
    {
        _emailStatusService = emailStatusService;
        _logger = logger;
    }

    /// <summary>
    /// Get email sending status for a specific verification event
    /// </summary>
    /// <param name="email">The email address</param>
    /// <param name="eventId">The verification event ID</param>
    /// <returns>Email sending status result</returns>
    [HttpGet("status/{email}/{eventId}")]
    public async Task<ActionResult<ApiResponse<EmailSendingResult>>> GetEmailSendingStatus(string email, string eventId)
    {
        try
        {
            var result = await _emailStatusService.GetEmailSendingStatusAsync(email, eventId);
            
            if (result == null)
            {
                return NotFoundError<EmailSendingResult>($"No email sending record found for event {eventId} and email {email}");
            }
            
            return Success(result, "Email sending status retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email sending status for {Email}, event {EventId}", email, eventId);
            return InternalError<EmailSendingResult>("Failed to retrieve email sending status");
        }
    }

    /// <summary>
    /// Get email sending statistics for a specific email address
    /// </summary>
    /// <param name="email">The email address</param>
    /// <returns>Email sending statistics</returns>
    [HttpGet("stats/{email}")]
    public async Task<ActionResult<EmailSendingStats>> GetEmailSendingStats(string email)
    {
        try
        {
            var stats = await _emailStatusService.GetEmailSendingStatsAsync(email);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email sending stats for {Email}", email);
            return StatusCode(500, "Internal server error");
        }
    }
}