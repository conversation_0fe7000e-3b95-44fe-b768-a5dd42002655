namespace Curio.Api.Constants;

/// <summary>
/// API 响应状态码常量
/// 采用"HTTP 状态码 + 两位业务码"的模式
/// </summary>
public static class ApiCodes
{
    #region 成功响应 (2xxxx)
    
    /// <summary>
    /// 操作成功
    /// </summary>
    public const int Success = 20000;
    
    /// <summary>
    /// 创建成功
    /// </summary>
    public const int Created = 20001;
    
    /// <summary>
    /// 更新成功
    /// </summary>
    public const int Updated = 20002;
    
    /// <summary>
    /// 删除成功
    /// </summary>
    public const int Deleted = 20003;
    
    #endregion

    #region 客户端错误 (4xxxx)

    #region 400xx - 请求错误
    
    /// <summary>
    /// 参数验证失败
    /// </summary>
    public const int ValidationFailed = 40001;
    
    /// <summary>
    /// 参数格式错误
    /// </summary>
    public const int InvalidFormat = 40002;
    
    /// <summary>
    /// 参数缺失
    /// </summary>
    public const int MissingParameter = 40003;
    
    #endregion

    #region 401xx - 认证错误
    
    /// <summary>
    /// Token 无效
    /// </summary>
    public const int InvalidToken = 40101;
    
    /// <summary>
    /// Token 过期
    /// </summary>
    public const int TokenExpired = 40102;
    
    /// <summary>
    /// Token 缺失
    /// </summary>
    public const int MissingToken = 40103;
    
    #endregion

    #region 403xx - 权限错误
    
    /// <summary>
    /// 权限不足
    /// </summary>
    public const int InsufficientPermissions = 40301;
    
    /// <summary>
    /// 账户被禁用
    /// </summary>
    public const int AccountDisabled = 40302;
    
    #endregion

    #region 404xx - 资源不存在
    
    /// <summary>
    /// 资源不存在
    /// </summary>
    public const int ResourceNotFound = 40401;
    
    /// <summary>
    /// 接口不存在
    /// </summary>
    public const int EndpointNotFound = 40402;
    
    #endregion

    #region 409xx - 资源冲突
    
    /// <summary>
    /// 资源已存在
    /// </summary>
    public const int ResourceExists = 40901;
    
    /// <summary>
    /// 邮箱已被注册
    /// </summary>
    public const int EmailAlreadyRegistered = 40902;
    
    #endregion

    #region 422xx - 业务逻辑错误
    
    /// <summary>
    /// 业务规则验证失败
    /// </summary>
    public const int BusinessRuleValidationFailed = 42201;
    
    /// <summary>
    /// 状态转换无效
    /// </summary>
    public const int InvalidStateTransition = 42202;
    
    /// <summary>
    /// 验证码无效
    /// </summary>
    public const int InvalidVerificationCode = 42203;
    
    /// <summary>
    /// 验证码已过期
    /// </summary>
    public const int VerificationCodeExpired = 42204;
    
    #endregion

    #region 429xx - 限流错误
    
    /// <summary>
    /// 请求频率超限
    /// </summary>
    public const int RateLimitExceeded = 42901;
    
    /// <summary>
    /// 并发请求过多
    /// </summary>
    public const int TooManyConcurrentRequests = 42902;
    
    #endregion

    #endregion

    #region 服务器错误 (5xxxx)

    #region 500xx - 内部错误
    
    /// <summary>
    /// 内部服务器错误
    /// </summary>
    public const int InternalServerError = 50001;
    
    /// <summary>
    /// 数据库连接失败
    /// </summary>
    public const int DatabaseConnectionFailed = 50002;
    
    #endregion

    #region 502xx - 网关错误
    
    /// <summary>
    /// 上游服务不可用
    /// </summary>
    public const int UpstreamServiceUnavailable = 50201;
    
    /// <summary>
    /// 服务响应超时
    /// </summary>
    public const int ServiceTimeout = 50202;
    
    #endregion

    #region 503xx - 服务不可用
    
    /// <summary>
    /// 服务维护中
    /// </summary>
    public const int ServiceMaintenance = 50301;
    
    /// <summary>
    /// 服务过载
    /// </summary>
    public const int ServiceOverload = 50302;
    
    #endregion

    #endregion
}