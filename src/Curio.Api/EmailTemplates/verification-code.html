<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Code</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 20px;
        }
        .title {
            font-size: 20px;
            margin-bottom: 20px;
            color: #1f2937;
        }
        .verification-code {
            text-align: center;
            margin: 30px 0;
        }
        .code {
            display: inline-block;
            background-color: #f3f4f6;
            border: 2px dashed #d1d5db;
            padding: 15px 25px;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 5px;
            color: #2563eb;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
        }
        .message {
            margin: 20px 0;
            line-height: 1.6;
        }
        .expiry-notice {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 12px 16px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .security-note {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 12px 16px;
            margin: 20px 0;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Curio</div>
            <h1 class="title">
                {{#ifEquals purpose "registration"}}
                    Welcome to Curio!
                {{else}}
                    Verification Code Login
                {{/ifEquals}}
            </h1>
        </div>

        <div class="message">
            <p>Hello,</p>
            <p>
                {{#ifEquals purpose "registration"}}
                    Thank you for choosing Curio! To ensure account security, please use the following verification code to complete your registration:
                {{else}}
                    You are attempting to log in to Curio. Please use the following verification code:
                {{/ifEquals}}
            </p>
        </div>

        <div class="verification-code">
            <div class="code">{{code}}</div>
        </div>

        <div class="expiry-notice">
            <strong>Note:</strong> This verification code will expire at <strong>{{formatDate expiresAt "yyyy-MM-dd HH:mm:ss"}}</strong>. Please use it promptly.
        </div>

        <div class="message">
            <p>Please enter this verification code in the application to continue. If you did not request this verification code, please ignore this email.</p>
        </div>

        <div class="security-note">
            <strong>Security Reminder:</strong>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li>Do not share your verification code with anyone</li>
                <li>Curio staff will never proactively ask for your verification code</li>
                <li>If you have any questions, please contact our customer service team</li>
            </ul>
        </div>

        <div class="footer">
            <p>This email is sent automatically by the system. Please do not reply directly.</p>
            <p>&copy; {{formatDate "now" "yyyy"}} Curio. All rights reserved.</p>
        </div>
    </div>
</body>
</html>