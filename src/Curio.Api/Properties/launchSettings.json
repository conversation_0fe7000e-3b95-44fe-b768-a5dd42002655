{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"Development": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:7274;http://localhost:5063", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Local": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:7274;http://localhost:5063", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Production": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:80", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}}, "Testing": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:7274;http://localhost:5063", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Testing"}}}}