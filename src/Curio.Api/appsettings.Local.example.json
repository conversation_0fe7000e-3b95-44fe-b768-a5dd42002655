{"_comment": "Example of appsettings.Local.json - Copy this to appsettings.Local.json and customize", "Application": {"Environment": "Development", "Api": {"BaseUrl": "https://localhost:7274"}, "Security": {"Jwt": {"SecretKey": "my-local-jwt-secret-key-32-characters-minimum-for-security", "Issuer": "Curio.Local", "Audience": "Curio.Api.Local"}, "Encryption": {"Key": "my-local-encryption-key-32-chars", "Salt": "my-local-salt-16"}}}, "Database": {"Host": "localhost", "Port": 5432, "Database": "curio", "Username": "curio", "Password": "my-local-database-password"}, "Orleans": {"ClusterId": "curio-cluster-local", "ServiceId": "curio-service-local"}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams-local", "SecurityProtocol": "PLAINTEXT"}, "Email": {"Smtp": {"Host": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "your-app-password", "EnableSsl": true}, "DefaultSender": {"FromEmail": "<EMAIL>", "FromName": "Curio Local Dev"}}}