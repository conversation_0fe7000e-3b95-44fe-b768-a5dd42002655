{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Curio.Infrastructure.Services": "Information", "Orleans": "Warning", "Microsoft.Orleans": "Error"}}, "Application": {"Name": "Curio API", "Version": "1.0.0", "Environment": "Production", "Api": {"BaseUrl": "https://api.curio.com", "AllowedHosts": ["api.curio.com"], "RequestTimeoutSeconds": 30, "MaxRequestBodySize": 10485760, "Cors": {"Enabled": true, "AllowedOrigins": ["https://curio.com", "https://www.curio.com", "https://app.curio.com"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Authorization", "Content-Type", "Accept"], "AllowCredentials": true}}, "Security": {"Jwt": {"Issuer": "Curio.Production", "Audience": "Curio.Api.Production", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "_comment": "SecretKey will be injected via environment variable JWT_SECRET_KEY"}, "Encryption": {"_comment": "Key and Salt will be injected via environment variables ENCRYPTION_KEY and ENCRYPTION_SALT"}}}, "Database": {"Host": "curio-db.internal", "Port": 5432, "Database": "curio_production", "Username": "curio_api", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false, "_comment": "Password will be injected via environment variable DATABASE_PASSWORD"}, "Orleans": {"ClusterId": "curio-cluster-prod", "ServiceId": "curio-service-prod", "Clustering": {"Provider": "AdoNet", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120, "_comment": "ConnectionString will use database connection automatically"}, "Storage": {"DefaultProvider": "AdoNet", "UseJsonFormat": true, "_comment": "ConnectionString will use database connection automatically"}, "Streaming": {"Provider": "Kafka", "_comment": "ConnectionString will use Kafka brokers automatically"}, "Reminders": {"Provider": "AdoNet", "_comment": "ConnectionString will use database connection automatically"}}, "Kafka": {"BrokerList": ["kafka-1.internal:9092", "kafka-2.internal:9092", "kafka-3.internal:9092"], "ConsumerGroupId": "orleans-event-streams-prod", "Topics": ["domain-events", "verification-events", "user-events", "email-events", "dead-letter-queue", "projection-rebuild"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "SecurityProtocol": "SASL_SSL", "SaslMechanism": "PLAIN", "_comment": "SaslUsername and SaslPassword will be injected via environment variables"}, "Email": {"Smtp": {"Host": "smtp.sendgrid.net", "Port": 587, "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false, "_comment": "Username and Password will be injected via environment variables"}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON>", "ReplyToEmail": "<EMAIL>", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}, "ResilientPublish": {"MaxRetries": 5, "BaseDelayMs": 1000, "BackupDirectory": "/var/log/curio/failed-events", "EnableDeadLetterQueue": true, "EnableLocalBackup": true, "PublishTimeoutSeconds": 30}}